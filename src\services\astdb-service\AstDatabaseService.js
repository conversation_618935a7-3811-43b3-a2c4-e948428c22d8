"use strict";
/**
 * AST Database Service
 * Provides backend functionality for AST database operations*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstDatabaseService = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const astdb_1 = require("@services/astdb");
const AstDatabaseMonitor_1 = require("./AstDatabaseMonitor");
const AstDatabaseContextService_1 = require("./AstDatabaseContextService");
const common_1 = require("@shared/proto/common");
const astdb_2 = require("@shared/proto/astdb");
/**
 * Normalize path for cross-platform comparison
 * Handles different path separators and resolves relative paths*/
function normalizePath(inputPath) {
    return path.resolve(inputPath).toLowerCase().replace(/\\/g, "/");
}
class AstDatabaseService {
    constructor() {
        this.astDb = null;
        this.scanner = null;
        this.logger = (0, astdb_1.createLogger)("AstDatabaseService", astdb_1.LogLevel.INFO);
        this.isScanning = false;
        this.scanProgressCallback = null;
        this.currentScanProgress = null;
        this.workspacePath = null;
        this.monitor = AstDatabaseMonitor_1.AstDatabaseMonitor.getInstance();
        this.logger.info("AstDatabaseService initialized");
    }
    static getInstance() {
        if (!AstDatabaseService.instance) {
            AstDatabaseService.instance = new AstDatabaseService();
        }
        return AstDatabaseService.instance;
    }
    /**
     * Check if a scan is currently in progress
     */ isCurrentlyScanning() {
        return this.isScanning;
    }
    /**
     * Get the current workspace path, with fallback logic
     */
    getCurrentWorkspacePath() {
        if (this.workspacePath) {
            return this.workspacePath;
        }
        // Try to get from VSCode workspace
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return workspaceFolders[0].uri.fsPath;
        }
        return null;
    }
    /** * Initialize the service with workspace path
     */
    async initialize(workspacePath) {
        // Use provided path or try to detect current workspace
        const resolvedWorkspacePath = workspacePath || this.getCurrentWorkspacePath();
        if (!resolvedWorkspacePath) {
            throw new Error("No workspace path provided and no VSCode workspace folders available");
        }
        this.workspacePath = resolvedWorkspacePath;
        this.logger.info("Initializing AST database service", { workspacePath: this.workspacePath });
        try {
            // Initialize AST database
            const dbPath = path.join(this.workspacePath, ".vscode", "ast-cache", "ast.json");
            this.astDb = new astdb_1.AstDB(dbPath);
            this.scanner = new astdb_1.WorkspaceScanner(this.astDb);
            this.logger.info("AST database service initialized successfully");
        }
        catch (error) {
            this.logger.error("Failed to initialize AST database service", error);
            throw error;
        }
    }
    /** * Ensure the service is initialized with current workspace
     */
    async ensureInitialized() {
        if (!this.astDb || !this.workspacePath) {
            // Try to auto-initialize if we have a workspace
            const currentWorkspacePath = this.getCurrentWorkspacePath();
            if (currentWorkspacePath) {
                await this.initialize(currentWorkspacePath);
            }
            else {
                throw new Error("AST database not initialized and no workspace available");
            }
        }
    }
    /** * Get current database status*/
    async getDatabaseStatus(request) {
        this.logger.debug("Getting database status");
        try {
            await this.ensureInitialized();
        }
        catch (error) {
            // If initialization fails, return an empty response indicating no database
            this.logger.warn("Database not available", error);
            return astdb_2.DatabaseStatusResponse.fromPartial({});
        }
        try {
            if (!this.astDb) {
                throw new Error("AST database is not available after initialization");
            }
            // Force a status update to ensure all counters are current
            this.astDb.updateStatus({});
            // Explicitly get the latest status after the update
            const detailedStatus = this.astDb.getDetailedStatus();
            this.logger.info("Current database status", {
                astate: detailedStatus.astate,
                filesTotal: detailedStatus.filesTotal,
                filesUnparsed: detailedStatus.filesUnparsed,
                astIndexFilesTotal: detailedStatus.astIndexFilesTotal, astIndexSymbolsTotal: detailedStatus.astIndexSymbolsTotal,
                astIndexUsagesTotal: detailedStatus.astIndexUsagesTotal,
            });
            const status = astdb_2.DatabaseStatus.fromPartial({ astate: detailedStatus.astate,
                filesTotal: detailedStatus.filesTotal,
                filesUnparsed: detailedStatus.filesUnparsed, astIndexFilesTotal: detailedStatus.astIndexFilesTotal,
                astIndexSymbolsTotal: detailedStatus.astIndexSymbolsTotal,
                astIndexUsagesTotal: detailedStatus.astIndexUsagesTotal,
                astMaxFilesHit: detailedStatus.astMaxFilesHit,
                lastUpdated: detailedStatus.lastUpdated.toISOString(),
                dbSizeBytes: detailedStatus.dbSizeBytes, uniqueFiles: detailedStatus.uniqueFiles,
                averageDefinitionsPerFile: detailedStatus.averageDefinitionsPerFile,
                averageUsagesPerDefinition: detailedStatus.averageUsagesPerDefinition,
            });
            return astdb_2.DatabaseStatusResponse.fromPartial({ status });
        }
        catch (error) {
            this.logger.error("Failed to get database status", error);
            throw error;
        }
    }
    /**
     * Start workspace scan
     */
    async startWorkspaceScan(request) {
        // Get current VSCode workspace folder
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            const errorMsg = "No workspace folder is currently open";
            this.logger.error(errorMsg);
            throw new Error(errorMsg);
        }
        const currentWorkspacePath = workspaceFolders[0].uri.fsPath;
        this.logger.info("Current VSCode workspace path", { currentWorkspacePath });
        // Use the current VSCode workspace path for scanning
        const workspacePath = currentWorkspacePath;
        this.logger.info("Starting workspace scan", {
            workspacePath,
            options: request.options,
        });
        // Ensure service is initialized with the correct workspace
        if (!this.astDb || !this.scanner || this.workspacePath !== workspacePath) {
            await this.initialize(workspacePath);
        }
        if (this.isScanning) {
            throw new Error("Scan already in progress");
        }
        try {
            this.isScanning = true;
            const scanOptions = {
                maxFiles: request.options?.maxFiles || 10000,
                includeExtensions: request.options?.includeExtensions || [
                    "js",
                    "jsx",
                    "ts",
                    "tsx",
                    "py",
                    "rs",
                    "go",
                    "c",
                    "h",
                    "cpp", "hpp",
                    "cs",
                    "rb",
                    "java",
                    "php",
                    "swift",
                    "kt",
                ],
                excludePatterns: request.options?.excludePatterns || ["node_modules", ".git", "dist", "build", ".vscode"],
                onProgress: (progress) => {
                    this.scanProgressCallback?.(progress);
                },
            };
            // Start scan in background
            this.performScan(workspacePath, scanOptions).catch((error) => {
                this.logger.error("Scan failed", error);
                this.isScanning = false;
            });
            return common_1.Empty.fromPartial({});
        }
        catch (error) {
            this.isScanning = false;
            this.logger.error("Failed to start workspace scan", error);
            throw error;
        }
    }
    /** * Set scan progress callback*/
    setScanProgressCallback(callback) {
        this.scanProgressCallback = callback;
    }
    /*** Get current scan progress */
    getCurrentScanProgress() {
        return this.currentScanProgress;
    }
    /**
     * Get scan progress for gRPC
     */ async getScanProgress(request) {
        const progress = this.getCurrentScanProgress();
        if (!progress) {
            // If no progress is available but we have a database, check if it's in a ready state
            // This helps ensure the UI shows the correct status after a scan completes
            if (this.astDb && !this.isScanning) {
                const status = this.astDb.getStatus();
                if (status.astate === "ready") {
                    // Create a completed progress object to signal completion to the UI
                    const completedProgress = {
                        totalFiles: status.astIndexFilesTotal,
                        processedFiles: status.astIndexFilesTotal,
                        currentFile: "",
                        errors: [],
                        status: "complete", startTime: new Date().toISOString(),
                        elapsedMs: 0,
                        estimatedRemainingMs: 0,
                        filesPerSecond: 0, definitionsFound: status.astIndexSymbolsTotal,
                        usagesFound: status.astIndexUsagesTotal, bytesProcessed: 0,
                    };
                    return astdb_2.ScanProgressResponse.fromPartial({ progress: completedProgress });
                }
            }
            return astdb_2.ScanProgressResponse.fromPartial({});
        }
        const scanProgress = {
            totalFiles: progress.totalFiles,
            processedFiles: progress.processedFiles,
            currentFile: progress.currentFile,
            errors: progress.errors,
            status: progress.status,
            startTime: progress.startTime.toISOString(),
            elapsedMs: progress.elapsedMs,
            estimatedRemainingMs: progress.estimatedRemainingMs,
            filesPerSecond: progress.filesPerSecond,
            definitionsFound: progress.definitionsFound, usagesFound: progress.usagesFound,
            bytesProcessed: progress.bytesProcessed,
        };
        return astdb_2.ScanProgressResponse.fromPartial({ progress: scanProgress });
    }
    /**
     * Perform the actual scan*/
    async performScan(workspacePath, options) {
        try {
            if (!this.scanner) {
                throw new Error("Scanner not initialized");
            }
            // Add progress callback to options
            const scanOptionsWithProgress = {
                ...options,
                onProgress: (progress) => {
                    // Store current progress
                    this.currentScanProgress = progress; // Call external callback if set
                    this.scanProgressCallback?.(progress);
                },
            };
            await this.scanner.scanWorkspace(workspacePath, scanOptionsWithProgress);
            this.isScanning = false;
            this.currentScanProgress = null;
            this.logger.info("Workspace scan completed successfully");
            // Force a status update after scan completion to ensure UI is updated
            if (this.astDb) {
                // First update with ready state
                this.astDb.updateStatus({ astate: "ready" });
                // Then force a refresh of all counters
                const status = this.astDb.getDetailedStatus();
                this.logger.info("Updated database status after scan", {
                    astate: status.astate,
                    filesTotal: status.filesTotal,
                    filesUnparsed: status.filesUnparsed,
                    astIndexFilesTotal: status.astIndexFilesTotal,
                    astIndexSymbolsTotal: status.astIndexSymbolsTotal,
                    astIndexUsagesTotal: status.astIndexUsagesTotal,
                });
            }
        }
        catch (error) {
            this.isScanning = false;
            this.currentScanProgress = null;
            this.logger.error("Workspace scan failed", error);
            // Update status to reflect error state
            if (this.astDb) {
                this.astDb.updateStatus({ astate: "error" });
            }
            throw error;
        }
    }
    /**
     * Search definitions
     */
    async searchDefinitions(request) {
        this.logger.debug("Searching definitions", {
            symbolName: request.symbolName,
            limit: request.limit,
        });
        if (!this.astDb) {
            throw new Error("AST database not initialized");
        }
        try {
            const definitions = await this.astDb.searchDefinitions(request.symbolName, request.limit || 50);
            const protoDefinitions = definitions.map((def) => ({
                officialPath: def.officialPath, symbolType: def.symbolType, resolvedType: def.resolvedType,
                thisIsAClass: def.thisIsAClass,
                thisClassDerivedFrom: def.thisClassDerivedFrom,
                cpath: def.cpath,
                declLine1: def.declLine1,
                declLine2: def.declLine2,
                bodyLine1: def.bodyLine1, bodyLine2: def.bodyLine2,
                usages: def.usages.map((usage) => ({
                    targetsForGuesswork: usage.targetsForGuesswork,
                    resolvedAs: usage.resolvedAs,
                    debugHint: usage.debugHint,
                    uline: usage.uline,
                })),
            }));
            return astdb_2.SearchDefinitionsResponse.fromPartial({ definitions: protoDefinitions });
        }
        catch (error) {
            this.logger.error("Failed to search definitions", error);
            throw error;
        }
    }
    /**
     * Get the AST database instance
     */ getAstDb() {
        return this.astDb;
    }
    /** * Get the monitor instance
     */
    getMonitor() {
        return this.monitor;
    }
    /**
     * Get context for autocomplete
     */
    async getContext(request) {
        const contextService = AstDatabaseContextService_1.AstDatabaseContextService.getInstance();
        return await contextService.getContext(request);
    }
    /**
     * Get related symbols
     */
    async getRelatedSymbols(request) {
        const contextService = AstDatabaseContextService_1.AstDatabaseContextService.getInstance();
        return await contextService.getRelatedSymbols(request);
    }
    /**
     * Get database statistics
     */
    async getStatistics(request) {
        const contextService = AstDatabaseContextService_1.AstDatabaseContextService.getInstance();
        return await contextService.getStatistics(request);
    }
    /**
     * Clear database
     */
    async clearDatabase(request) {
        this.logger.debug("Clearing database");
        try {
            await this.ensureInitialized();
            if (!this.astDb) {
                throw new Error("AST database not initialized");
            }
            // Clear the database
            await this.astDb.clearAll();
            this.logger.info("Database cleared successfully");
            return common_1.Empty.fromPartial({});
        }
        catch (error) {
            this.logger.error("Failed to clear database", error);
            throw error;
        }
    }
    /**
     * Get performance metrics
     */
    getPerformanceMetrics() {
        return this.monitor.getMetrics();
    }
    /**
     * Get health status
     */
    getHealthStatus() {
        return this.monitor.getHealthStatus();
    }
    /**
     * Get status report
     */
    getStatusReport() {
        const metrics = this.getPerformanceMetrics();
        const health = this.getHealthStatus();
        return `AST Database Status Report:
Health: ${health.status}
Total Requests: ${metrics.totalRequests}
Success Rate: ${((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1)}%
Average Response Time: ${metrics.averageResponseTime.toFixed(1)}ms
Error Rate: ${(metrics.errorRate * 100).toFixed(1)}%
Uptime: ${(metrics.uptime / 1000 / 60).toFixed(1)} minutes`;
    }
    /**
     * Dispose resources
     */
    async dispose() {
        this.logger.info("Disposing AST database service");
        if (this.astDb) {
            await this.astDb.close();
            this.astDb = null;
        }
        this.monitor.dispose();
        this.isScanning = false;
        this.currentScanProgress = null;
    }
}
exports.AstDatabaseService = AstDatabaseService;
AstDatabaseService.instance = null;
