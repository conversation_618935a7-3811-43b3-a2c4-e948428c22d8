/**
 * AST Database Integration Tests
 * Tests the complete scan database functionality including:
 * - Real workspace scanning
 * - Progress monitoring
 * - Status updates
 * - Configuration management
 */

import * as assert from "assert"
import * as path from "path"
import * as fs from "fs/promises"
import * as vscode from "vscode"
import { AstDatabaseService } from "../src/services/astdb-service"
import { StartScanRequest } from "../src/shared/proto/astdb"

describe("AST Database Integration Tests", function () {
	this.timeout(60000) // 60 seconds timeout for scanning operations

	let astService: AstDatabaseService
	let testWorkspacePath: string
	let testFiles: string[]

	before(async function () {
		// Create a temporary test workspace
		testWorkspacePath = path.join(__dirname, "test-workspace")
		await createTestWorkspace()

		// Initialize AST Database Service
		astService = AstDatabaseService.getInstance()
		await astService.initialize(testWorkspacePath)
	})

	after(async function () {
		// Clean up test workspace
		await cleanupTestWorkspace()
	})

	describe("Real Workspace Scanning", function () {
		it("should scan workspace and find all test files", async function () {
			const scanRequest = StartScanRequest.create({
				workspacePath: testWorkspacePath,
				options: {
					maxFiles: 100,
					includeExtensions: ["js", "ts", "py"],
					excludePatterns: ["node_modules/**", ".git/**"],
				},
			})

			// Start the scan
			await astService.startWorkspaceScan(scanRequest)

			// Wait for scan to complete
			await waitForScanCompletion()

			// Verify database status
			const statusResponse = await astService.getDatabaseStatus({ metadata: undefined })
			assert.ok(statusResponse.status, "Database status should be available")
			assert.strictEqual(statusResponse.status.astate, "ready", "Database should be ready")
			assert.ok(statusResponse.status.astIndexFilesTotal > 0, "Should have indexed files")
			assert.ok(statusResponse.status.astIndexSymbolsTotal > 0, "Should have found symbols")
		})

		it("should provide real-time progress updates", async function () {
			const progressUpdates: any[] = []

			// Monitor progress during scan
			const progressMonitor = setInterval(async () => {
				try {
					const progressResponse = await astService.getScanProgress({ metadata: undefined })
					if (progressResponse.progress) {
						progressUpdates.push({
							...progressResponse.progress,
							timestamp: Date.now(),
						})
					}
				} catch (error) {
					// Ignore errors during monitoring
				}
			}, 100)

			// Start a new scan
			const scanRequest = StartScanRequest.create({
				workspacePath: testWorkspacePath,
				options: {
					maxFiles: 50,
					includeExtensions: ["js", "ts"],
					excludePatterns: ["node_modules/**"],
				},
			})

			await astService.startWorkspaceScan(scanRequest)
			await waitForScanCompletion()

			clearInterval(progressMonitor)

			// Verify progress updates
			assert.ok(progressUpdates.length > 0, "Should have received progress updates")

			// Check that progress started from 0 and reached completion
			const firstUpdate = progressUpdates[0]
			const lastUpdate = progressUpdates[progressUpdates.length - 1]

			assert.strictEqual(firstUpdate.processedFiles, 0, "Should start with 0 processed files")
			assert.ok(lastUpdate.processedFiles >= lastUpdate.totalFiles, "Should complete all files")
		})
	})

	describe("Progress Bar Display", function () {
		it("should calculate progress percentage correctly", function () {
			// Test progress calculation logic
			const testCases = [
				{ processed: 0, total: 100, expected: 0 },
				{ processed: 50, total: 100, expected: 50 },
				{ processed: 100, total: 100, expected: 100 },
				{ processed: 0, total: 0, expected: 0 }, // Edge case
			]

			testCases.forEach(({ processed, total, expected }) => {
				const percentage = total > 0 ? Math.round((processed / total) * 100) : 0
				assert.strictEqual(percentage, expected, `Progress calculation failed for ${processed}/${total}`)
			})
		})

		it("should handle null progress gracefully", function () {
			// Test that null progress doesn't cause errors
			const progress = null
			const processedFiles = progress?.processedFiles || 0
			const totalFiles = progress?.totalFiles || 0
			const currentFile = progress?.currentFile || ""

			assert.strictEqual(processedFiles, 0)
			assert.strictEqual(totalFiles, 0)
			assert.strictEqual(currentFile, "")
		})
	})

	describe("Configuration Management", function () {
		it("should handle auto-scan configuration", async function () {
			// This test would require VSCode extension context
			// For now, we'll test the configuration structure
			const defaultConfig = {
				astDatabaseAutoScan: false,
				astDatabaseMaxFiles: 1000,
				astDatabaseIncludeExtensions: ["js", "jsx", "ts", "tsx", "py"],
				astDatabaseExcludePatterns: ["node_modules/**", ".git/**", "dist/**"],
			}

			// Verify default configuration structure
			assert.strictEqual(typeof defaultConfig.astDatabaseAutoScan, "boolean")
			assert.strictEqual(typeof defaultConfig.astDatabaseMaxFiles, "number")
			assert.ok(Array.isArray(defaultConfig.astDatabaseIncludeExtensions))
			assert.ok(Array.isArray(defaultConfig.astDatabaseExcludePatterns))
		})
	})

	// Helper functions
	async function createTestWorkspace(): Promise<void> {
		await fs.mkdir(testWorkspacePath, { recursive: true })

		// Create test files with different languages
		testFiles = ["src/main.ts", "src/utils.js", "src/component.tsx", "scripts/build.py", "README.md"]

		for (const filePath of testFiles) {
			const fullPath = path.join(testWorkspacePath, filePath)
			await fs.mkdir(path.dirname(fullPath), { recursive: true })

			let content = ""
			if (filePath.endsWith(".ts") || filePath.endsWith(".tsx")) {
				content = `
export interface TestInterface {
	name: string;
	value: number;
}

export class TestClass implements TestInterface {
	constructor(public name: string, public value: number) {}
	
	public getValue(): number {
		return this.value;
	}
}

export function testFunction(param: string): TestInterface {
	return new TestClass(param, 42);
}
`
			} else if (filePath.endsWith(".js")) {
				content = `
function utilFunction(input) {
	return input.toString();
}

const CONSTANT_VALUE = 100;

module.exports = {
	utilFunction,
	CONSTANT_VALUE
};
`
			} else if (filePath.endsWith(".py")) {
				content = `
class PythonClass:
	def __init__(self, name):
		self.name = name
	
	def get_name(self):
		return self.name

def python_function(value):
	return value * 2
`
			} else {
				content = "# Test file content"
			}

			await fs.writeFile(fullPath, content)
		}
	}

	async function cleanupTestWorkspace(): Promise<void> {
		try {
			await fs.rm(testWorkspacePath, { recursive: true, force: true })
		} catch (error) {
			// Ignore cleanup errors
		}
	}

	async function waitForScanCompletion(): Promise<void> {
		const maxWaitTime = 30000 // 30 seconds
		const checkInterval = 500 // 500ms
		let elapsed = 0

		while (elapsed < maxWaitTime) {
			try {
				const statusResponse = await astService.getDatabaseStatus({ metadata: undefined })
				if (statusResponse.status?.astate === "ready") {
					return
				}
			} catch (error) {
				// Continue waiting
			}

			await new Promise((resolve) => setTimeout(resolve, checkInterval))
			elapsed += checkInterval
		}

		throw new Error("Scan did not complete within timeout")
	}
})
