/**
 * AST Database Context Service
 * Provides context retrieval functionality for AST database operations*/

import * as vscode from "vscode"
import * as path from "path"
import * as fs from "fs/promises"
import {
	AstDB,
	createLogger,
	LogLevel,
} from "@services/astdb"
import { retrieveAstBasedExtraContext, SimpleTokenizer, getSymbolContext, getRelatedSymbols } from "@services/astdb/completion-rag"
import { CursorPosition, PostprocessSettings } from "@services/astdb/ast-structs"
import { AstDatabaseService } from "./AstDatabaseService"
import { AstDatabaseMonitor } from "./AstDatabaseMonitor"
import {
	GetContextResponse,
	GetRelatedSymbolsResponse,
	GetStatisticsResponse,ContextFile,
} from "@shared/proto/astdb"
import type {
	GetContextRequest,GetRelatedSymbolsRequest,GetStatisticsRequest,
} from "@shared/proto/astdb"

export class AstDatabaseContextService {
	private static instance: AstDatabaseContextService | null = null;
	private logger = createLogger("AstDatabaseContextService", LogLevel.INFO);
	private astDbService: AstDatabaseService;

	private constructor() {
		this.astDbService = AstDatabaseService.getInstance();
		this.logger.info("AstDatabaseContextService initialized");
	}

	public static getInstance(): AstDatabaseContextService {
		if (!AstDatabaseContextService.instance) {
			AstDatabaseContextService.instance = new AstDatabaseContextService();}
		return AstDatabaseContextService.instance;}

	/** * Get context for autocomplete
	 */
	public async getContext(request: GetContextRequest): Promise<GetContextResponse> {
		const monitor = this.astDbService.getMonitor();
		const requestId = monitor.recordRequestStart();
		const startTime = Date.now();
		this.logger.debug("Getting context", {
			filePath: request.filePath,
			line: request.line,
			character: request.character,
			requestId,
		});
		const astDb = this.astDbService.getAstDb();
		if (!astDb) {
			const error = new Error("AST database not initialized");
			monitor.recordRequestFailure(requestId, error, Date.now() - startTime);
			throw error;
		}

		try {
			// Create cursor position object
			const cursorPos: CursorPosition = {
				file: request.filePath,
				line: request.line,
				character: request.character,
			};

			// Create tokenizer
			const tokenizer = new SimpleTokenizer("chat", 0.5);

			// Post-processing settings
			const ppSettings: PostprocessSettings = {
				maxFilesN: 5,
				maxTokensPerFile: request.maxTokens || 1000,
			};

			// Context tracking
			const contextUsed: Record<string, any> = {};
			// Get project directories
			const workspaceFolders = vscode.workspace.workspaceFolders;
			const projectDirs = workspaceFolders ? workspaceFolders.map(folder => folder.uri.fsPath) : [];

			// Retrieve context
			const extraContext = await retrieveAstBasedExtraContext(
				astDb,
				tokenizer,
				cursorPos.file,
				cursorPos,
				[Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER], // No ignore range
				ppSettings,
				request.maxTokens || 1000, // RAG tokens budget
				contextUsed,
				projectDirs,
			);
			
			// Load file contents for context files
			const contextFiles: ContextFile[] = [];
			for (const fileInfo of contextUsed.attached_files || []) {
				try {
					// Read file content
					const fileContentData = await fs.readFile(fileInfo.file_name, 'utf8');
					
					// Extract relevant lines
					const lines = fileContentData.split('\n');
					const startLine = Math.max(0, fileInfo.line1 - 1);
					const endLine = Math.min(lines.length, fileInfo.line2);
					const relevantContent = lines.slice(startLine, endLine).join('\n');
					
					contextFiles.push(ContextFile.create({
						fileName: path.relative(projectDirs[0] || '', fileInfo.file_name),
						fileContent: relevantContent,
						line1: fileInfo.line1,
						line2: fileInfo.line2,
						symbols: contextUsed.bucket_declarations?.map((decl: any) => decl.name) || [],
						gradientType: 4,
						usefulness: 100.0,
					}));
				} catch (error) {
					this.logger.warn(`Failed to read file ${fileInfo.file_name}:`, error as Error);
				}
			}
			
			monitor.recordRequestSuccess(requestId, Date.now() - startTime);
			return GetContextResponse.create({
				contextFiles: contextFiles,
				extraContext: extraContext,
			});
		} catch (error) {
			monitor.recordRequestFailure(requestId, error as Error, Date.now() - startTime);
			this.logger.error("Failed to get context", error as Error);
			throw error;
		}
	}
	
	/** * Get related symbols */
	public async getRelatedSymbols(request: GetRelatedSymbolsRequest): Promise<GetRelatedSymbolsResponse> {
		const monitor = this.astDbService.getMonitor();
		const requestId = monitor.recordRequestStart();
		const startTime = Date.now();
		this.logger.debug("Getting related symbols", {
			symbolPath: request.symbolPath,
			maxResults: request.maxResults,
			requestId,
		});
		const astDb = this.astDbService.getAstDb();
		if (!astDb) {
			const error = new Error("AST database not initialized");
			monitor.recordRequestFailure(requestId, error, Date.now() - startTime);
			throw error;
		}
		try {
			const symbols = await getRelatedSymbols(astDb, request.symbolPath, request.maxResults || 10);
			monitor.recordRequestSuccess(requestId, Date.now() - startTime);
			return GetRelatedSymbolsResponse.create({ symbols });
		} catch (error) {
			monitor.recordRequestFailure(requestId, error as Error, Date.now() - startTime);
			this.logger.error("Failed to get related symbols", error as Error);
			throw error;
		}
	}
	/*** Get database statistics
	 */
	public async getStatistics(request: GetStatisticsRequest): Promise<GetStatisticsResponse> {
		const monitor = this.astDbService.getMonitor();
		const requestId = monitor.recordRequestStart();
		const startTime = Date.now();
		this.logger.debug("Getting database statistics", { requestId });
		const astDb = this.astDbService.getAstDb();
		if (!astDb) {
			const error = new Error("AST database not initialized");
			monitor.recordRequestFailure(requestId, error, Date.now() - startTime);
			throw error;
		}

		try {
			const stats = astDb.getStatistics();
			const fileDefinitionCounts = stats.filesWithMostDefinitions.map(item => ({
				file: item.file,
				count: item.count,
			}));
			const statistics = {
				totalDefinitions: stats.totalDefinitions,
				totalUsages: stats.totalUsages,
				totalFiles: stats.totalFiles,
				definitionsByType: stats.definitionsByType,
				filesWithMostDefinitions: fileDefinitionCounts,
			};
			monitor.recordRequestSuccess(requestId, Date.now() - startTime);
			return GetStatisticsResponse.create({ statistics });
		} catch (error) {
			monitor.recordRequestFailure(requestId, error as Error, Date.now() - startTime);
			this.logger.error("Failed to get statistics", error as Error);
			throw error;
		}
	}
}
