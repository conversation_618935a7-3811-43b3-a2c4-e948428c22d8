"use strict";
/**
 * AST Database Context Service
 * Provides context retrieval functionality for AST database operations*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstDatabaseContextService = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs/promises"));
const astdb_1 = require("@services/astdb");
const completion_rag_1 = require("@services/astdb/completion-rag");
const AstDatabaseService_1 = require("./AstDatabaseService");
const astdb_2 = require("@shared/proto/astdb");
class AstDatabaseContextService {
    constructor() {
        this.logger = (0, astdb_1.createLogger)("AstDatabaseContextService", astdb_1.LogLevel.INFO);
        this.astDbService = AstDatabaseService_1.AstDatabaseService.getInstance();
        this.logger.info("AstDatabaseContextService initialized");
    }
    static getInstance() {
        if (!AstDatabaseContextService.instance) {
            AstDatabaseContextService.instance = new AstDatabaseContextService();
        }
        return AstDatabaseContextService.instance;
    }
    /** * Get context for autocomplete
     */
    async getContext(request) {
        const monitor = this.astDbService.getMonitor();
        const requestId = monitor.recordRequestStart();
        const startTime = Date.now();
        this.logger.debug("Getting context", {
            filePath: request.filePath,
            line: request.line,
            character: request.character,
            requestId,
        });
        const astDb = this.astDbService.getAstDb();
        if (!astDb) {
            const error = new Error("AST database not initialized");
            monitor.recordRequestFailure(requestId, error, Date.now() - startTime);
            throw error;
        }
        try {
            // Create cursor position object
            const cursorPos = {
                file: request.filePath,
                line: request.line,
                character: request.character,
            };
            // Create tokenizer
            const tokenizer = new completion_rag_1.SimpleTokenizer("chat", 0.5);
            // Post-processing settings
            const ppSettings = {
                maxFilesN: 5,
                maxTokensPerFile: request.maxTokens || 1000,
            };
            // Context tracking
            const contextUsed = {};
            // Get project directories
            const workspaceFolders = vscode.workspace.workspaceFolders;
            const projectDirs = workspaceFolders ? workspaceFolders.map(folder => folder.uri.fsPath) : [];
            // Retrieve context
            const extraContext = await (0, completion_rag_1.retrieveAstBasedExtraContext)(astDb, tokenizer, cursorPos.file, cursorPos, [Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER], // No ignore range
            ppSettings, request.maxTokens || 1000, // RAG tokens budget
            contextUsed, projectDirs);
            // Load file contents for context files
            const contextFiles = [];
            for (const fileInfo of contextUsed.attached_files || []) {
                try {
                    // Read file content
                    const fileContentData = await fs.readFile(fileInfo.file_name, 'utf8');
                    // Extract relevant lines
                    const lines = fileContentData.split('\n');
                    const startLine = Math.max(0, fileInfo.line1 - 1);
                    const endLine = Math.min(lines.length, fileInfo.line2);
                    const relevantContent = lines.slice(startLine, endLine).join('\n');
                    contextFiles.push(astdb_2.ContextFile.fromPartial({
                        fileName: path.relative(projectDirs[0] || '', fileInfo.file_name),
                        fileContent: relevantContent,
                        line1: fileInfo.line1,
                        line2: fileInfo.line2,
                        symbols: contextUsed.bucket_declarations?.map((decl) => decl.name) || [],
                        gradientType: 4,
                        usefulness: 100.0,
                    }));
                }
                catch (error) {
                    this.logger.warn(`Failed to read file ${fileInfo.file_name}:`, error);
                }
            }
            monitor.recordRequestSuccess(requestId, Date.now() - startTime);
            return astdb_2.GetContextResponse.fromPartial({
                contextFiles: contextFiles,
                extraContext: extraContext,
            });
        }
        catch (error) {
            monitor.recordRequestFailure(requestId, error, Date.now() - startTime);
            this.logger.error("Failed to get context", error);
            throw error;
        }
    }
    /** * Get related symbols */
    async getRelatedSymbols(request) {
        const monitor = this.astDbService.getMonitor();
        const requestId = monitor.recordRequestStart();
        const startTime = Date.now();
        this.logger.debug("Getting related symbols", {
            symbolPath: request.symbolPath,
            maxResults: request.maxResults,
            requestId,
        });
        const astDb = this.astDbService.getAstDb();
        if (!astDb) {
            const error = new Error("AST database not initialized");
            monitor.recordRequestFailure(requestId, error, Date.now() - startTime);
            throw error;
        }
        try {
            const symbols = await (0, completion_rag_1.getRelatedSymbols)(astDb, request.symbolPath, request.maxResults || 10);
            monitor.recordRequestSuccess(requestId, Date.now() - startTime);
            return astdb_2.GetRelatedSymbolsResponse.fromPartial({ symbols });
        }
        catch (error) {
            monitor.recordRequestFailure(requestId, error, Date.now() - startTime);
            this.logger.error("Failed to get related symbols", error);
            throw error;
        }
    }
    /*** Get database statistics
     */
    async getStatistics(request) {
        const monitor = this.astDbService.getMonitor();
        const requestId = monitor.recordRequestStart();
        const startTime = Date.now();
        this.logger.debug("Getting database statistics", { requestId });
        const astDb = this.astDbService.getAstDb();
        if (!astDb) {
            const error = new Error("AST database not initialized");
            monitor.recordRequestFailure(requestId, error, Date.now() - startTime);
            throw error;
        }
        try {
            const stats = astDb.getStatistics();
            const fileDefinitionCounts = stats.filesWithMostDefinitions.map(item => ({
                file: item.file,
                count: item.count,
            }));
            const statistics = {
                totalDefinitions: stats.totalDefinitions,
                totalUsages: stats.totalUsages,
                totalFiles: stats.totalFiles,
                definitionsByType: stats.definitionsByType,
                filesWithMostDefinitions: fileDefinitionCounts,
            };
            monitor.recordRequestSuccess(requestId, Date.now() - startTime);
            return astdb_2.GetStatisticsResponse.fromPartial({ statistics });
        }
        catch (error) {
            monitor.recordRequestFailure(requestId, error, Date.now() - startTime);
            this.logger.error("Failed to get statistics", error);
            throw error;
        }
    }
}
exports.AstDatabaseContextService = AstDatabaseContextService;
AstDatabaseContextService.instance = null;
