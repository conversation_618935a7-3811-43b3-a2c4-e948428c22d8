/**
 * AST Database Service
 * Provides backend functionality for AST database operations*/

import * as vscode from "vscode"
import * as path from "path"
import {
	AstDB,
	WorkspaceScanner,
	scanWorkspaceForAST,
	formatScanProgress,
	createProgressReport,
	createLogger,
	LogLevel,
	type ScanProgress,type ScanOptions,
} from "@services/astdb"
import { AstDatabaseMonitor } from "./AstDatabaseMonitor"
import { EmptyRequest, Empty } from "@shared/proto/common"
import {
	DatabaseStatus,
	DatabaseStatusResponse,
	ScanProgressResponse,
	ScanProgress as ProtoScanProgress,
	SearchDefinitionsResponse,
} from "@shared/proto/astdb"
import type {
	StartScanRequest,
	SearchDefinitionsRequest,
	ClearDatabaseRequest,
} from "@shared/proto/astdb"

/**
 * Normalize path for cross-platform comparison
 * Handles different path separators and resolves relative paths*/
function normalizePath(inputPath: string): string {
	return path.resolve(inputPath).toLowerCase().replace(/\\/g, "/")
}

export class AstDatabaseService {
	private static instance: AstDatabaseService | null = null;
	private astDb: AstDB | null = null;
	private scanner: WorkspaceScanner | null = null;
	private logger = createLogger("AstDatabaseService", LogLevel.INFO);
	private isScanning = false;
	private scanProgressCallback: ((progress: ScanProgress) => void) | null = null;
	private currentScanProgress: ScanProgress | null = null;
	private workspacePath: string | null = null;
	private monitor = AstDatabaseMonitor.getInstance();

	private constructor() {
		this.logger.info("AstDatabaseService initialized");
	}public static getInstance(): AstDatabaseService {
		if (!AstDatabaseService.instance) {
			AstDatabaseService.instance = new AstDatabaseService();
		}
		return AstDatabaseService.instance;}

	/**
	 * Check if a scan is currently in progress
	 */public isCurrentlyScanning(): boolean {
		return this.isScanning;
	}

	/**
	 * Get the current workspace path, with fallback logic
	 */
	private getCurrentWorkspacePath(): string | null {// First try to use the stored workspace path
		if (this.workspacePath) {
			return this.workspacePath;
		}

		// Try to get from VSCode workspace
		const workspaceFolders = vscode.workspace.workspaceFolders;
		if (workspaceFolders && workspaceFolders.length > 0) {
			return workspaceFolders[0].uri.fsPath;
		}

		return null;
	}

	/** * Initialize the service with workspace path
	 */
	public async initialize(workspacePath?: string): Promise<void> {
		// Use provided path or try to detect current workspace
		const resolvedWorkspacePath = workspacePath || this.getCurrentWorkspacePath();

		if (!resolvedWorkspacePath) {
			throw new Error("No workspace path provided and no VSCode workspace folders available");
		}

		this.workspacePath = resolvedWorkspacePath;this.logger.info("Initializing AST database service", { workspacePath: this.workspacePath });

		try {
			// Initialize AST database
			const dbPath = path.join(this.workspacePath, ".vscode", "ast-cache", "ast.json");
			this.astDb = new AstDB(dbPath);
			this.scanner = new WorkspaceScanner(this.astDb);
			this.logger.info("AST database service initialized successfully");
		} catch (error) {
			this.logger.error("Failed to initialize AST database service", error as Error);
			throw error;
		}
	}

	/** * Ensure the service is initialized with current workspace
	 */
	public async ensureInitialized(): Promise<void> {
		if (!this.astDb || !this.workspacePath) {
			// Try to auto-initialize if we have a workspace
			const currentWorkspacePath = this.getCurrentWorkspacePath();
			if (currentWorkspacePath) {
				await this.initialize(currentWorkspacePath);} else {throw new Error("AST database not initialized and no workspace available");
			}
		}
	}

	/** * Get current database status*/
	public async getDatabaseStatus(request: EmptyRequest): Promise<DatabaseStatusResponse> {this.logger.debug("Getting database status");

		try {await this.ensureInitialized();} catch (error) {
			// If initialization fails, return an empty response indicating no database
			this.logger.warn("Database not available", error as Error);
			return DatabaseStatusResponse.create({});
		}
		try {
			if (!this.astDb) {
				throw new Error("AST database is not available after initialization");
			}

			// Force a status update to ensure all counters are current
			this.astDb.updateStatus({});

			// Explicitly get the latest status after the update
			const detailedStatus = this.astDb.getDetailedStatus();

			this.logger.info("Current database status", {
				astate: detailedStatus.astate,
				filesTotal: detailedStatus.filesTotal,
				filesUnparsed: detailedStatus.filesUnparsed,
				astIndexFilesTotal: detailedStatus.astIndexFilesTotal,astIndexSymbolsTotal: detailedStatus.astIndexSymbolsTotal,
				astIndexUsagesTotal: detailedStatus.astIndexUsagesTotal,});

			const status = DatabaseStatus.create({astate: detailedStatus.astate,
				filesTotal: detailedStatus.filesTotal,
				filesUnparsed: detailedStatus.filesUnparsed,astIndexFilesTotal: detailedStatus.astIndexFilesTotal,
				astIndexSymbolsTotal: detailedStatus.astIndexSymbolsTotal,
				astIndexUsagesTotal: detailedStatus.astIndexUsagesTotal,
				astMaxFilesHit: detailedStatus.astMaxFilesHit,
				lastUpdated: detailedStatus.lastUpdated.toISOString(),
				dbSizeBytes: detailedStatus.dbSizeBytes,uniqueFiles: detailedStatus.uniqueFiles,
				averageDefinitionsPerFile: detailedStatus.averageDefinitionsPerFile,
				averageUsagesPerDefinition: detailedStatus.averageUsagesPerDefinition,
			});
			return DatabaseStatusResponse.create({ status });
		} catch (error) {this.logger.error("Failed to get database status", error as Error);
			throw error;
		}
	}

	/**
	 * Start workspace scan
	 */
	public async startWorkspaceScan(request: StartScanRequest): Promise<Empty> {
		// Get current VSCode workspace folder
		const workspaceFolders = vscode.workspace.workspaceFolders;
		if (!workspaceFolders || workspaceFolders.length === 0) {
			const errorMsg = "No workspace folder is currently open";this.logger.error(errorMsg);
			throw new Error(errorMsg);}
		const currentWorkspacePath = workspaceFolders[0].uri.fsPath;
		this.logger.info("Current VSCode workspace path", { currentWorkspacePath });

		// Use the current VSCode workspace path for scanning
		const workspacePath = currentWorkspacePath;

		this.logger.info("Starting workspace scan", {
			workspacePath,
			options: request.options,});
		// Ensure service is initialized with the correct workspace
		if (!this.astDb || !this.scanner || this.workspacePath !== workspacePath) {await this.initialize(workspacePath);
		}

		if (this.isScanning) {
			throw new Error("Scan already in progress");}

		try {
			this.isScanning = true;

			const scanOptions: ScanOptions = {
				maxFiles: request.options?.maxFiles || 10000,
				includeExtensions: request.options?.includeExtensions || [
					"js",
					"jsx",
					"ts",
					"tsx",
					"py",
					"rs",
					"go",
					"c",
					"h",
					"cpp","hpp",
					"cs",
					"rb",
					"java",
					"php",
					"swift",
					"kt",],
				excludePatterns: request.options?.excludePatterns || ["node_modules", ".git", "dist", "build", ".vscode"],
				onProgress: (progress) => {
					this.scanProgressCallback?.(progress);
				},
			};

			// Start scan in background
			this.performScan(workspacePath, scanOptions).catch((error) => {
				this.logger.error("Scan failed", error as Error);
				this.isScanning = false;
			});

			return Empty.create({});
		} catch (error) {
			this.isScanning = false;
			this.logger.error("Failed to start workspace scan", error as Error);
			throw error;
		}
	}

	/** * Set scan progress callback*/
	public setScanProgressCallback(callback: (progress: ScanProgress) => void): void {
		this.scanProgressCallback = callback;
	}

	/*** Get current scan progress */
	public getCurrentScanProgress(): ScanProgress | null {
		return this.currentScanProgress;
	}

	/**
	 * Get scan progress for gRPC
	 */public async getScanProgress(request: EmptyRequest): Promise<ScanProgressResponse> {const progress = this.getCurrentScanProgress();

		if (!progress) {
			// If no progress is available but we have a database, check if it's in a ready state
			// This helps ensure the UI shows the correct status after a scan completes
			if (this.astDb && !this.isScanning) {
				const status = this.astDb.getStatus();
				if (status.astate === "ready") {
					// Create a completed progress object to signal completion to the UI
					const completedProgress: ProtoScanProgress = {
						totalFiles: status.astIndexFilesTotal,
						processedFiles: status.astIndexFilesTotal,
						currentFile: "",
						errors: [],
						status: "complete",startTime: new Date().toISOString(),
						elapsedMs: 0,
						estimatedRemainingMs:0,
						filesPerSecond: 0,definitionsFound: status.astIndexSymbolsTotal,
						usagesFound: status.astIndexUsagesTotal,bytesProcessed:0,
					};
					return ScanProgressResponse.create({ progress: completedProgress });
				}
			}return ScanProgressResponse.create({});
		}

		const scanProgress: ProtoScanProgress = {
			totalFiles: progress.totalFiles,
			processedFiles: progress.processedFiles,
			currentFile: progress.currentFile,
			errors: progress.errors,
			status: progress.status,
			startTime: progress.startTime.toISOString(),
			elapsedMs: progress.elapsedMs,
			estimatedRemainingMs: progress.estimatedRemainingMs,
			filesPerSecond: progress.filesPerSecond,
			definitionsFound: progress.definitionsFound,usagesFound: progress.usagesFound,
			bytesProcessed: progress.bytesProcessed,
		};

		return ScanProgressResponse.create({ progress: scanProgress });
	}

	/**
	 * Perform the actual scan*/
	private async performScan(workspacePath: string, options: ScanOptions): Promise<void> {
		try {
			if (!this.scanner) {
				throw new Error("Scanner not initialized");
			}

			// Add progress callback to options
			const scanOptionsWithProgress: ScanOptions = {
				...options,
				onProgress: (progress: ScanProgress) => {
					// Store current progress
					this.currentScanProgress = progress;// Call external callback if set
					this.scanProgressCallback?.(progress);
				},
			};

			await this.scanner.scanWorkspace(workspacePath, scanOptionsWithProgress);this.isScanning = false;
			this.currentScanProgress = null;
			this.logger.info("Workspace scan completed successfully");
			// Force a status update after scan completion to ensure UI is updated
			if (this.astDb) {
				// First update with ready state
				this.astDb.updateStatus({ astate: "ready" });
				// Then force a refresh of all counters
				const status = this.astDb.getDetailedStatus();this.logger.info("Updated database status after scan", {
					astate: status.astate,
					filesTotal: status.filesTotal,
					filesUnparsed: status.filesUnparsed,
					astIndexFilesTotal: status.astIndexFilesTotal,
					astIndexSymbolsTotal: status.astIndexSymbolsTotal,
					astIndexUsagesTotal: status.astIndexUsagesTotal,
				});
			}
		} catch (error) {this.isScanning = false;this.currentScanProgress = null;
			this.logger.error("Workspace scan failed", error as Error);
			// Update status to reflect error state
			if (this.astDb) {
				this.astDb.updateStatus({ astate: "error" });}
			throw error;
		}
	}

	/**
	 * Search definitions
	 */
	public async searchDefinitions(request: SearchDefinitionsRequest): Promise<SearchDefinitionsResponse> {this.logger.debug("Searching definitions", {
			symbolName: request.symbolName,
			limit: request.limit,
		});

		if (!this.astDb) {
			throw new Error("AST database not initialized");
		}
		try {const definitions = await this.astDb.searchDefinitions(request.symbolName, request.limit || 50);
			const protoDefinitions = definitions.map((def) => ({
				officialPath: def.officialPath,symbolType: def.symbolType,resolvedType: def.resolvedType,
				thisIsAClass: def.thisIsAClass,
				thisClassDerivedFrom: def.thisClassDerivedFrom,
				cpath: def.cpath,
				declLine1: def.declLine1,
				declLine2: def.declLine2,
				bodyLine1: def.bodyLine1,bodyLine2: def.bodyLine2,
				usages: def.usages.map((usage) => ({
					targetsForGuesswork: usage.targetsForGuesswork,
					resolvedAs: usage.resolvedAs,
					debugHint: usage.debugHint,
					uline: usage.uline,})),
			}));

			return SearchDefinitionsResponse.create({ definitions: protoDefinitions });
		} catch (error) {
			this.logger.error("Failed to search definitions", error as Error);
			throw error;
		}
	}

	/**
	 * Get the AST database instance
	 */public getAstDb(): AstDB | null {
		return this.astDb;
	}

	/** * Get the monitor instance
	 */
	public getMonitor(): AstDatabaseMonitor {
		return this.monitor;
	}
}
