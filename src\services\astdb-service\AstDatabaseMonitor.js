"use strict";
/**
 * AST Database Monitor
 * Provides monitoring, health checks, and performance tracking for AST database operations
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstDatabaseMonitor = void 0;
const AstDatabaseService_1 = require("./AstDatabaseService");
const astdb_1 = require("@services/astdb");
class AstDatabaseMonitor {
    constructor() {
        this.logger = (0, astdb_1.createLogger)("AstDatabaseMonitor", astdb_1.LogLevel.INFO);
        this.healthCheckInterval = null;
        this.metricsResetInterval = null;
        this.startTime = new Date();
        this.metrics = this.initializeMetrics();
        this.healthStatus = this.initializeHealthStatus();
        this.startMonitoring();
    }
    static getInstance() {
        if (!AstDatabaseMonitor.instance) {
            AstDatabaseMonitor.instance = new AstDatabaseMonitor();
        }
        return AstDatabaseMonitor.instance;
    }
    initializeMetrics() {
        return {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            timeouts: 0,
            averageResponseTime: 0,
            maxResponseTime: 0,
            minResponseTime: Number.MAX_SAFE_INTEGER,
            lastRequestTime: null,
            uptime: 0,
            errorRate: 0,
        };
    }
    initializeHealthStatus() {
        return {
            isHealthy: true,
            status: "healthy",
            issues: [],
            lastCheck: new Date(),
            databaseSize: 0,
            memoryUsage: 0,
        };
    }
    startMonitoring() {
        // Health check every 30 seconds
        this.healthCheckInterval = setInterval(() => {
            this.performHealthCheck();
        }, 30000);
        // Reset metrics every hour to prevent overflow
        this.metricsResetInterval = setInterval(() => {
            this.resetMetrics();
        }, 3600000);
        this.logger.info("AST Database monitoring started");
    }
    /**
     * Record a request start
     */
    recordRequestStart() {
        const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.metrics.totalRequests++;
        this.metrics.lastRequestTime = new Date();
        return requestId;
    }
    /**
     * Record a successful request
     */
    recordRequestSuccess(requestId, responseTimeMs) {
        this.metrics.successfulRequests++;
        this.updateResponseTimeMetrics(responseTimeMs);
        this.updateErrorRate();
        this.logger.debug(`Request ${requestId} completed successfully in ${responseTimeMs}ms`);
    }
    /**
     * Record a failed request
     */
    recordRequestFailure(requestId, error, responseTimeMs) {
        this.metrics.failedRequests++;
        if (error.message.includes("timeout")) {
            this.metrics.timeouts++;
        }
        this.updateResponseTimeMetrics(responseTimeMs);
        this.updateErrorRate();
        this.logger.warn(`Request ${requestId} failed after ${responseTimeMs}ms:`, error);
    }
    updateResponseTimeMetrics(responseTime) {
        // Update average response time
        const totalSuccessful = this.metrics.successfulRequests;
        if (totalSuccessful === 1) {
            this.metrics.averageResponseTime = responseTime;
        }
        else {
            this.metrics.averageResponseTime =
                (this.metrics.averageResponseTime * (totalSuccessful - 1) + responseTime) / totalSuccessful;
        }
        // Update min/max response times
        this.metrics.maxResponseTime = Math.max(this.metrics.maxResponseTime, responseTime);
        this.metrics.minResponseTime = Math.min(this.metrics.minResponseTime, responseTime);
    }
    updateErrorRate() {
        if (this.metrics.totalRequests > 0) {
            this.metrics.errorRate = this.metrics.failedRequests / this.metrics.totalRequests;
        }
    }
    /**
     * Perform health check
     */
    async performHealthCheck() {
        const issues = [];
        let status = "healthy";
        try {
            // Check error rate
            if (this.metrics.errorRate > 0.5) {
                issues.push(`High error rate: ${(this.metrics.errorRate * 100).toFixed(1)}%`);
                status = "unhealthy";
            }
            else if (this.metrics.errorRate > 0.2) {
                issues.push(`Elevated error rate: ${(this.metrics.errorRate * 100).toFixed(1)}%`);
                status = "degraded";
            }
            // Check response time
            if (this.metrics.averageResponseTime > 5000) {
                issues.push(`High average response time: ${this.metrics.averageResponseTime.toFixed(0)}ms`);
                status = status === "unhealthy" ? "unhealthy" : "degraded";
            }
            // Check timeout rate
            const timeoutRate = this.metrics.totalRequests > 0 ? this.metrics.timeouts / this.metrics.totalRequests : 0;
            if (timeoutRate > 0.1) {
                issues.push(`High timeout rate: ${(timeoutRate * 100).toFixed(1)}%`);
                status = "unhealthy";
            }
            // Check database service availability
            const astService = AstDatabaseService_1.AstDatabaseService.getInstance();
            if (astService.isCurrentlyScanning()) {
                issues.push("Database is currently scanning");
                status = status === "unhealthy" ? "unhealthy" : "degraded";
            }
            // Update memory usage (approximate)
            const memoryUsage = process.memoryUsage();
            this.healthStatus.memoryUsage = memoryUsage.heapUsed;
            // Update uptime
            this.metrics.uptime = Date.now() - this.startTime.getTime();
            this.healthStatus = {
                isHealthy: status === "healthy",
                status,
                issues,
                lastCheck: new Date(),
                databaseSize: this.healthStatus.databaseSize, // Keep previous value
                memoryUsage: this.healthStatus.memoryUsage,
            };
            if (issues.length > 0) {
                this.logger.warn(`Health check found issues: ${issues.join(", ")}`);
            }
        }
        catch (error) {
            this.logger.error("Health check failed", error);
            this.healthStatus = {
                isHealthy: false,
                status: "unhealthy",
                issues: [`Health check failed: ${error}`],
                lastCheck: new Date(),
                databaseSize: 0,
                memoryUsage: 0,
            };
        }
    }
    /**
     * Get current performance metrics
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * Get current health status
     */
    getHealthStatus() {
        return { ...this.healthStatus };
    }
    /**
     * Reset metrics
     */
    resetMetrics() {
        this.logger.info("Resetting performance metrics");
        this.metrics = this.initializeMetrics();
    }
    /**
     * Get formatted status report
     */
    getStatusReport() {
        const metrics = this.getMetrics();
        const health = this.getHealthStatus();
        const uptimeHours = (metrics.uptime / (1000 * 60 * 60)).toFixed(1);
        const successRate = metrics.totalRequests > 0 ? ((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1) : "0";
        return `
AST Database Status Report
=========================
Health: ${health.status.toUpperCase()} ${health.isHealthy ? "✓" : "✗"}
Uptime: ${uptimeHours} hours
Total Requests: ${metrics.totalRequests}
Success Rate: ${successRate}%
Average Response Time: ${metrics.averageResponseTime.toFixed(0)}ms
Memory Usage: ${(health.memoryUsage / 1024 / 1024).toFixed(1)}MB

${health.issues.length > 0 ? `Issues:\n${health.issues.map((issue) => `- ${issue}`).join("\n")}` : "No issues detected"}
		`.trim();
    }
    /**
     * Dispose of resources
     */
    dispose() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
        }
        if (this.metricsResetInterval) {
            clearInterval(this.metricsResetInterval);
            this.metricsResetInterval = null;
        }
        this.logger.info("AST Database monitoring stopped");
        AstDatabaseMonitor.instance = null;
    }
}
exports.AstDatabaseMonitor = AstDatabaseMonitor;
AstDatabaseMonitor.instance = null;
