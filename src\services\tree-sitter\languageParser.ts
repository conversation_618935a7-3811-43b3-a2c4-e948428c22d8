import * as path from "path"
import * as fs from "fs"
import * as vscode from "vscode"
import Parser from "web-tree-sitter"
import {
	javascriptQuery,
	typescriptQ<PERSON>y,
	pythonQuery,
	rustQuery,
	goQuery,
	cppQuery,
	cQuery,
	csharpQuery,
	rubyQuery,
	javaQuery,
	phpQuery,
	swiftQuery,
	kotlinQuery,
} from "./queries"

export interface LanguageParser {
	[key: string]: {
		parser: Parser
		query: Parser.Query
	}
}

let isParserInitialized = false

/**
 * Initializes the Tree-sitter parser if it hasn't been initialized yet
 * @returns A promise that resolves when the parser is initialized
 */
async function initializeParser() {
	if (!isParserInitialized) {
		console.log("Initializing tree-sitter parser...")
		await Parser.init()
		isParserInitialized = true
		console.log("Tree-sitter parser initialized successfully")
	}
	return isParserInitialized
}

/**
 * Resolves the extension path in a more reliable way
 * @returns The path to the extension's root directory
 */
function getExtensionPath(): string {
	// Try to get the extension path from VSCode API if available
	try {
		const extension = vscode.extensions.getExtension("cline.cline")
		if (extension) {
			return extension.extensionPath
		}
	} catch (error) {
		console.log("Could not get extension path from VSCode API, falling back to __dirname")
	}

	// Fallback: calculate from __dirname
	// In a VSCode extension, __dirname is typically:
	// - In development: <extension_root>/out/src/services/tree-sitter
	// - In production: <extension_root>/dist/src/services/tree-sitter or similar
	// We need to go up several levels to reach the extension root
	let extensionPath = __dirname

	// Go up to find the extension root (where package.json is located)
	for (let i = 0; i < 5; i++) {
		// Try up to 5 levels up
		const testPath = path.dirname(extensionPath)
		const packageJsonPath = path.join(testPath, "package.json")

		try {
			if (fs.existsSync(packageJsonPath)) {
				return testPath // Found the extension root
			}
		} catch (error) {
			// Ignore errors and continue
		}
		extensionPath = testPath
	}

	// If we couldn't find package.json, use a reasonable default
	return path.dirname(path.dirname(path.dirname(__dirname)))
}

/**
 * Checks if a file exists at the given path
 * @param filePath The path to check
 * @returns True if the file exists, false otherwise
 */
function fileExists(filePath: string): boolean {
	try {
		return fs.existsSync(filePath)
	} catch (error) {
		return false
	}
}

/**
 * Loads a Tree-sitter language parser from a WebAssembly file
 * @param langName The name of the language to load
 * @returns A Promise that resolves to the loaded language
 * @throws Error if the language cannot be loaded from any of the attempted paths
 */
async function loadLanguage(langName: string): Promise<Parser.Language> {
	// Ensure the parser is initialized before loading languages
	await initializeParser()

	// Get the extension path in a more reliable way
	const extensionPath = getExtensionPath()

	// Define possible locations for the WASM file
	// The primary location is the dist directory within the extension
	const possiblePaths = [
		path.join(extensionPath, "dist", `tree-sitter-${langName}.wasm`),
		path.join(extensionPath, "out", `tree-sitter-${langName}.wasm`),
		path.join(extensionPath, "assets", `tree-sitter-${langName}.wasm`),
		path.join(extensionPath, "wasm", `tree-sitter-${langName}.wasm`),
		path.join(extensionPath, `tree-sitter-${langName}.wasm`),
	]

	console.log(`Loading language WASM: ${langName}`)
	console.log(`__dirname: ${__dirname}`)
	console.log(`Resolved extension path: ${extensionPath}`)
	// Log which paths actually exist to help with debugging
	const existingPaths = possiblePaths.filter((p) => fileExists(p))
	console.log(`Found existing WASM files: ${existingPaths.length > 0 ? existingPaths.join(", ") : "None"}`)

	// Try each path in order
	for (const wasmPath of possiblePaths) {
		try {
			console.log(`Attempting to load ${langName} from ${wasmPath}`)
			// Check if the file exists before trying to load it
			if (!fileExists(wasmPath)) {
				console.log(`File does not exist: ${wasmPath}`)
				continue
			}

			const language = await Parser.Language.load(wasmPath)
			console.log(`Successfully loaded language ${langName} from ${wasmPath}`)
			return language
		} catch (error) {
			console.error(`Failed to load language ${langName} from ${wasmPath}:`, error)
		}
	}

	// If we get here, all paths failed
	const errorMsg = `Failed to load language ${langName} from all attempted paths. Please ensure the WASM file (tree-sitter-${langName}.wasm) is bundled with the extension during packaging. The files should be in the extension's installation directory (e.g., dist, out, assets, or wasm folder). Alternatively, set the TREE_SITTER_WASM_PATH environment variable to the directory containing the WASM files.`
	console.error(errorMsg)
	throw new Error(errorMsg)
}

/*
Using node bindings for tree-sitter is problematic in vscode extensions
because of incompatibility with electron. Going the .wasm route has the 
advantage of not having to build for multiple architectures.

We use web-tree-sitter and tree-sitter-wasms which provides auto-updating prebuilt WASM binaries for tree-sitter's language parsers.

This function loads WASM modules for relevant language parsers based on input files:
1. Extracts unique file extensions
2. Maps extensions to language names
3. Loads corresponding WASM files (containing grammar rules)
4. Uses WASM modules to initialize tree-sitter parsers

This approach optimizes performance by loading only necessary parsers once for all relevant files.

Sources:
- https://github.com/tree-sitter/node-tree-sitter/issues/169
- https://github.com/tree-sitter/node-tree-sitter/issues/168
- https://github.com/Gregoor/tree-sitter-wasms/blob/main/README.md
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/README.md
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/test/query-test.js
*/
export async function loadRequiredLanguageParsers(filesToParse: string[]): Promise<LanguageParser> {
	console.log(`Loading language parsers for ${filesToParse.length} files`)
	await initializeParser()
	const extensionsToLoad = new Set(filesToParse.map((file) => path.extname(file).toLowerCase().slice(1)))
	console.log(`Extensions to load: ${Array.from(extensionsToLoad).join(", ")}`)

	const parsers: LanguageParser = {}
	for (const ext of extensionsToLoad) {
		console.log(`Loading parser for extension: ${ext}`)
		let language: Parser.Language
		let query: Parser.Query

		switch (ext) {
			case "js":
			case "jsx":
				language = await loadLanguage("javascript")
				query = language.query(javascriptQuery)
				break
			case "ts":
				language = await loadLanguage("typescript")
				query = language.query(typescriptQuery)
				break
			case "tsx":
				language = await loadLanguage("tsx")
				query = language.query(typescriptQuery)
				break
			case "py":
				language = await loadLanguage("python")
				query = language.query(pythonQuery)
				break
			case "rs":
				language = await loadLanguage("rust")
				query = language.query(rustQuery)
				break
			case "go":
				language = await loadLanguage("go")
				query = language.query(goQuery)
				break
			case "cpp":
			case "hpp":
				language = await loadLanguage("cpp")
				query = language.query(cppQuery)
				break
			case "c":
			case "h":
				language = await loadLanguage("c")
				query = language.query(cQuery)
				break
			case "cs":
				language = await loadLanguage("c_sharp")
				query = language.query(csharpQuery)
				break
			case "rb":
				language = await loadLanguage("ruby")
				query = language.query(rubyQuery)
				break
			case "java":
				language = await loadLanguage("java")
				query = language.query(javaQuery)
				break
			case "php":
				language = await loadLanguage("php")
				query = language.query(phpQuery)
				break
			case "swift":
				language = await loadLanguage("swift")
				query = language.query(swiftQuery)
				break
			case "kt":
				language = await loadLanguage("kotlin")
				query = language.query(kotlinQuery)
				break
			default:
				throw new Error(`Unsupported language: ${ext}`)
		}

		const parser = new Parser()
		parser.setLanguage(language)
		parsers[ext] = { parser, query }
		console.log(`Successfully loaded parser for extension: ${ext}`)
	}
	console.log(`All language parsers loaded successfully. Total: ${Object.keys(parsers).length}`)
	return parsers
}
