"use strict"
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: astdb.proto
Object.defineProperty(exports, "__esModule", { value: true })
exports.AstDatabaseServiceDefinition =
	exports.GetStatisticsResponse =
	exports.DatabaseStatistics_DefinitionsByTypeEntry =
	exports.DatabaseStatistics =
	exports.FileDefinitionCount =
	exports.GetStatisticsRequest =
	exports.ClearDatabaseRequest =
	exports.GetRelatedSymbolsResponse =
	exports.GetRelatedSymbolsRequest =
	exports.GetContextResponse =
	exports.ContextFile =
	exports.GetContextRequest =
	exports.SearchDefinitionsResponse =
	exports.AstDefinition =
	exports.AstUsage =
	exports.SearchDefinitionsRequest =
	exports.DatabaseStatusResponse =
	exports.ScanProgressResponse =
	exports.StartScanRequest =
	exports.ScanOptions =
	exports.DatabaseStatus =
	exports.ScanProgress =
		void 0
/* eslint-disable */
const wire_1 = require("@bufbuild/protobuf/wire")
const common_1 = require("./common")
function createBaseScanProgress() {
	return {
		totalFiles: 0,
		processedFiles: 0,
		currentFile: "",
		errors: [],
		status: "",
		startTime: "",
		elapsedMs: 0,
		estimatedRemainingMs: 0,
		filesPerSecond: 0,
		definitionsFound: 0,
		usagesFound: 0,
		bytesProcessed: 0,
	}
}
exports.ScanProgress = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.totalFiles !== 0) {
			writer.uint32(8).int32(message.totalFiles)
		}
		if (message.processedFiles !== 0) {
			writer.uint32(16).int32(message.processedFiles)
		}
		if (message.currentFile !== "") {
			writer.uint32(26).string(message.currentFile)
		}
		for (const v of message.errors) {
			writer.uint32(34).string(v)
		}
		if (message.status !== "") {
			writer.uint32(42).string(message.status)
		}
		if (message.startTime !== "") {
			writer.uint32(50).string(message.startTime)
		}
		if (message.elapsedMs !== 0) {
			writer.uint32(56).int64(message.elapsedMs)
		}
		if (message.estimatedRemainingMs !== 0) {
			writer.uint32(64).int64(message.estimatedRemainingMs)
		}
		if (message.filesPerSecond !== 0) {
			writer.uint32(73).double(message.filesPerSecond)
		}
		if (message.definitionsFound !== 0) {
			writer.uint32(80).int32(message.definitionsFound)
		}
		if (message.usagesFound !== 0) {
			writer.uint32(88).int32(message.usagesFound)
		}
		if (message.bytesProcessed !== 0) {
			writer.uint32(96).int64(message.bytesProcessed)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseScanProgress()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 8) {
						break
					}
					message.totalFiles = reader.int32()
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}
					message.processedFiles = reader.int32()
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}
					message.currentFile = reader.string()
					continue
				}
				case 4: {
					if (tag !== 34) {
						break
					}
					message.errors.push(reader.string())
					continue
				}
				case 5: {
					if (tag !== 42) {
						break
					}
					message.status = reader.string()
					continue
				}
				case 6: {
					if (tag !== 50) {
						break
					}
					message.startTime = reader.string()
					continue
				}
				case 7: {
					if (tag !== 56) {
						break
					}
					message.elapsedMs = longToNumber(reader.int64())
					continue
				}
				case 8: {
					if (tag !== 64) {
						break
					}
					message.estimatedRemainingMs = longToNumber(reader.int64())
					continue
				}
				case 9: {
					if (tag !== 73) {
						break
					}
					message.filesPerSecond = reader.double()
					continue
				}
				case 10: {
					if (tag !== 80) {
						break
					}
					message.definitionsFound = reader.int32()
					continue
				}
				case 11: {
					if (tag !== 88) {
						break
					}
					message.usagesFound = reader.int32()
					continue
				}
				case 12: {
					if (tag !== 96) {
						break
					}
					message.bytesProcessed = longToNumber(reader.int64())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			totalFiles: isSet(object.totalFiles) ? globalThis.Number(object.totalFiles) : 0,
			processedFiles: isSet(object.processedFiles) ? globalThis.Number(object.processedFiles) : 0,
			currentFile: isSet(object.currentFile) ? globalThis.String(object.currentFile) : "",
			errors: globalThis.Array.isArray(object?.errors) ? object.errors.map((e) => globalThis.String(e)) : [],
			status: isSet(object.status) ? globalThis.String(object.status) : "",
			startTime: isSet(object.startTime) ? globalThis.String(object.startTime) : "",
			elapsedMs: isSet(object.elapsedMs) ? globalThis.Number(object.elapsedMs) : 0,
			estimatedRemainingMs: isSet(object.estimatedRemainingMs) ? globalThis.Number(object.estimatedRemainingMs) : 0,
			filesPerSecond: isSet(object.filesPerSecond) ? globalThis.Number(object.filesPerSecond) : 0,
			definitionsFound: isSet(object.definitionsFound) ? globalThis.Number(object.definitionsFound) : 0,
			usagesFound: isSet(object.usagesFound) ? globalThis.Number(object.usagesFound) : 0,
			bytesProcessed: isSet(object.bytesProcessed) ? globalThis.Number(object.bytesProcessed) : 0,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.totalFiles !== 0) {
			obj.totalFiles = Math.round(message.totalFiles)
		}
		if (message.processedFiles !== 0) {
			obj.processedFiles = Math.round(message.processedFiles)
		}
		if (message.currentFile !== "") {
			obj.currentFile = message.currentFile
		}
		if (message.errors?.length) {
			obj.errors = message.errors
		}
		if (message.status !== "") {
			obj.status = message.status
		}
		if (message.startTime !== "") {
			obj.startTime = message.startTime
		}
		if (message.elapsedMs !== 0) {
			obj.elapsedMs = Math.round(message.elapsedMs)
		}
		if (message.estimatedRemainingMs !== 0) {
			obj.estimatedRemainingMs = Math.round(message.estimatedRemainingMs)
		}
		if (message.filesPerSecond !== 0) {
			obj.filesPerSecond = message.filesPerSecond
		}
		if (message.definitionsFound !== 0) {
			obj.definitionsFound = Math.round(message.definitionsFound)
		}
		if (message.usagesFound !== 0) {
			obj.usagesFound = Math.round(message.usagesFound)
		}
		if (message.bytesProcessed !== 0) {
			obj.bytesProcessed = Math.round(message.bytesProcessed)
		}
		return obj
	},
	create(base) {
		return exports.ScanProgress.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseScanProgress()
		message.totalFiles = object.totalFiles ?? 0
		message.processedFiles = object.processedFiles ?? 0
		message.currentFile = object.currentFile ?? ""
		message.errors = object.errors?.map((e) => e) || []
		message.status = object.status ?? ""
		message.startTime = object.startTime ?? ""
		message.elapsedMs = object.elapsedMs ?? 0
		message.estimatedRemainingMs = object.estimatedRemainingMs ?? 0
		message.filesPerSecond = object.filesPerSecond ?? 0
		message.definitionsFound = object.definitionsFound ?? 0
		message.usagesFound = object.usagesFound ?? 0
		message.bytesProcessed = object.bytesProcessed ?? 0
		return message
	},
}
function createBaseDatabaseStatus() {
	return {
		astate: "",
		filesTotal: 0,
		filesUnparsed: 0,
		astIndexFilesTotal: 0,
		astIndexSymbolsTotal: 0,
		astIndexUsagesTotal: 0,
		astMaxFilesHit: false,
		lastUpdated: undefined,
		dbSizeBytes: undefined,
		uniqueFiles: undefined,
		averageDefinitionsPerFile: undefined,
		averageUsagesPerDefinition: undefined,
	}
}
exports.DatabaseStatus = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.astate !== "") {
			writer.uint32(10).string(message.astate)
		}
		if (message.filesTotal !== 0) {
			writer.uint32(16).int32(message.filesTotal)
		}
		if (message.filesUnparsed !== 0) {
			writer.uint32(24).int32(message.filesUnparsed)
		}
		if (message.astIndexFilesTotal !== 0) {
			writer.uint32(32).int32(message.astIndexFilesTotal)
		}
		if (message.astIndexSymbolsTotal !== 0) {
			writer.uint32(40).int32(message.astIndexSymbolsTotal)
		}
		if (message.astIndexUsagesTotal !== 0) {
			writer.uint32(48).int32(message.astIndexUsagesTotal)
		}
		if (message.astMaxFilesHit !== false) {
			writer.uint32(56).bool(message.astMaxFilesHit)
		}
		if (message.lastUpdated !== undefined) {
			writer.uint32(66).string(message.lastUpdated)
		}
		if (message.dbSizeBytes !== undefined) {
			writer.uint32(72).int64(message.dbSizeBytes)
		}
		if (message.uniqueFiles !== undefined) {
			writer.uint32(80).int32(message.uniqueFiles)
		}
		if (message.averageDefinitionsPerFile !== undefined) {
			writer.uint32(89).double(message.averageDefinitionsPerFile)
		}
		if (message.averageUsagesPerDefinition !== undefined) {
			writer.uint32(97).double(message.averageUsagesPerDefinition)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseDatabaseStatus()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.astate = reader.string()
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}
					message.filesTotal = reader.int32()
					continue
				}
				case 3: {
					if (tag !== 24) {
						break
					}
					message.filesUnparsed = reader.int32()
					continue
				}
				case 4: {
					if (tag !== 32) {
						break
					}
					message.astIndexFilesTotal = reader.int32()
					continue
				}
				case 5: {
					if (tag !== 40) {
						break
					}
					message.astIndexSymbolsTotal = reader.int32()
					continue
				}
				case 6: {
					if (tag !== 48) {
						break
					}
					message.astIndexUsagesTotal = reader.int32()
					continue
				}
				case 7: {
					if (tag !== 56) {
						break
					}
					message.astMaxFilesHit = reader.bool()
					continue
				}
				case 8: {
					if (tag !== 66) {
						break
					}
					message.lastUpdated = reader.string()
					continue
				}
				case 9: {
					if (tag !== 72) {
						break
					}
					message.dbSizeBytes = longToNumber(reader.int64())
					continue
				}
				case 10: {
					if (tag !== 80) {
						break
					}
					message.uniqueFiles = reader.int32()
					continue
				}
				case 11: {
					if (tag !== 89) {
						break
					}
					message.averageDefinitionsPerFile = reader.double()
					continue
				}
				case 12: {
					if (tag !== 97) {
						break
					}
					message.averageUsagesPerDefinition = reader.double()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			astate: isSet(object.astate) ? globalThis.String(object.astate) : "",
			filesTotal: isSet(object.filesTotal) ? globalThis.Number(object.filesTotal) : 0,
			filesUnparsed: isSet(object.filesUnparsed) ? globalThis.Number(object.filesUnparsed) : 0,
			astIndexFilesTotal: isSet(object.astIndexFilesTotal) ? globalThis.Number(object.astIndexFilesTotal) : 0,
			astIndexSymbolsTotal: isSet(object.astIndexSymbolsTotal) ? globalThis.Number(object.astIndexSymbolsTotal) : 0,
			astIndexUsagesTotal: isSet(object.astIndexUsagesTotal) ? globalThis.Number(object.astIndexUsagesTotal) : 0,
			astMaxFilesHit: isSet(object.astMaxFilesHit) ? globalThis.Boolean(object.astMaxFilesHit) : false,
			lastUpdated: isSet(object.lastUpdated) ? globalThis.String(object.lastUpdated) : undefined,
			dbSizeBytes: isSet(object.dbSizeBytes) ? globalThis.Number(object.dbSizeBytes) : undefined,
			uniqueFiles: isSet(object.uniqueFiles) ? globalThis.Number(object.uniqueFiles) : undefined,
			averageDefinitionsPerFile: isSet(object.averageDefinitionsPerFile)
				? globalThis.Number(object.averageDefinitionsPerFile)
				: undefined,
			averageUsagesPerDefinition: isSet(object.averageUsagesPerDefinition)
				? globalThis.Number(object.averageUsagesPerDefinition)
				: undefined,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.astate !== "") {
			obj.astate = message.astate
		}
		if (message.filesTotal !== 0) {
			obj.filesTotal = Math.round(message.filesTotal)
		}
		if (message.filesUnparsed !== 0) {
			obj.filesUnparsed = Math.round(message.filesUnparsed)
		}
		if (message.astIndexFilesTotal !== 0) {
			obj.astIndexFilesTotal = Math.round(message.astIndexFilesTotal)
		}
		if (message.astIndexSymbolsTotal !== 0) {
			obj.astIndexSymbolsTotal = Math.round(message.astIndexSymbolsTotal)
		}
		if (message.astIndexUsagesTotal !== 0) {
			obj.astIndexUsagesTotal = Math.round(message.astIndexUsagesTotal)
		}
		if (message.astMaxFilesHit !== false) {
			obj.astMaxFilesHit = message.astMaxFilesHit
		}
		if (message.lastUpdated !== undefined) {
			obj.lastUpdated = message.lastUpdated
		}
		if (message.dbSizeBytes !== undefined) {
			obj.dbSizeBytes = Math.round(message.dbSizeBytes)
		}
		if (message.uniqueFiles !== undefined) {
			obj.uniqueFiles = Math.round(message.uniqueFiles)
		}
		if (message.averageDefinitionsPerFile !== undefined) {
			obj.averageDefinitionsPerFile = message.averageDefinitionsPerFile
		}
		if (message.averageUsagesPerDefinition !== undefined) {
			obj.averageUsagesPerDefinition = message.averageUsagesPerDefinition
		}
		return obj
	},
	create(base) {
		return exports.DatabaseStatus.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseDatabaseStatus()
		message.astate = object.astate ?? ""
		message.filesTotal = object.filesTotal ?? 0
		message.filesUnparsed = object.filesUnparsed ?? 0
		message.astIndexFilesTotal = object.astIndexFilesTotal ?? 0
		message.astIndexSymbolsTotal = object.astIndexSymbolsTotal ?? 0
		message.astIndexUsagesTotal = object.astIndexUsagesTotal ?? 0
		message.astMaxFilesHit = object.astMaxFilesHit ?? false
		message.lastUpdated = object.lastUpdated ?? undefined
		message.dbSizeBytes = object.dbSizeBytes ?? undefined
		message.uniqueFiles = object.uniqueFiles ?? undefined
		message.averageDefinitionsPerFile = object.averageDefinitionsPerFile ?? undefined
		message.averageUsagesPerDefinition = object.averageUsagesPerDefinition ?? undefined
		return message
	},
}
function createBaseScanOptions() {
	return { maxFiles: undefined, includeExtensions: [], excludePatterns: [] }
}
exports.ScanOptions = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.maxFiles !== undefined) {
			writer.uint32(8).int32(message.maxFiles)
		}
		for (const v of message.includeExtensions) {
			writer.uint32(18).string(v)
		}
		for (const v of message.excludePatterns) {
			writer.uint32(26).string(v)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseScanOptions()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 8) {
						break
					}
					message.maxFiles = reader.int32()
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.includeExtensions.push(reader.string())
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}
					message.excludePatterns.push(reader.string())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			maxFiles: isSet(object.maxFiles) ? globalThis.Number(object.maxFiles) : undefined,
			includeExtensions: globalThis.Array.isArray(object?.includeExtensions)
				? object.includeExtensions.map((e) => globalThis.String(e))
				: [],
			excludePatterns: globalThis.Array.isArray(object?.excludePatterns)
				? object.excludePatterns.map((e) => globalThis.String(e))
				: [],
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.maxFiles !== undefined) {
			obj.maxFiles = Math.round(message.maxFiles)
		}
		if (message.includeExtensions?.length) {
			obj.includeExtensions = message.includeExtensions
		}
		if (message.excludePatterns?.length) {
			obj.excludePatterns = message.excludePatterns
		}
		return obj
	},
	create(base) {
		return exports.ScanOptions.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseScanOptions()
		message.maxFiles = object.maxFiles ?? undefined
		message.includeExtensions = object.includeExtensions?.map((e) => e) || []
		message.excludePatterns = object.excludePatterns?.map((e) => e) || []
		return message
	},
}
function createBaseStartScanRequest() {
	return { metadata: undefined, workspacePath: "", options: undefined }
}
exports.StartScanRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			common_1.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		if (message.workspacePath !== "") {
			writer.uint32(18).string(message.workspacePath)
		}
		if (message.options !== undefined) {
			exports.ScanOptions.encode(message.options, writer.uint32(26).fork()).join()
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseStartScanRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = common_1.Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.workspacePath = reader.string()
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}
					message.options = exports.ScanOptions.decode(reader, reader.uint32())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			metadata: isSet(object.metadata) ? common_1.Metadata.fromJSON(object.metadata) : undefined,
			workspacePath: isSet(object.workspacePath) ? globalThis.String(object.workspacePath) : "",
			options: isSet(object.options) ? exports.ScanOptions.fromJSON(object.options) : undefined,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = common_1.Metadata.toJSON(message.metadata)
		}
		if (message.workspacePath !== "") {
			obj.workspacePath = message.workspacePath
		}
		if (message.options !== undefined) {
			obj.options = exports.ScanOptions.toJSON(message.options)
		}
		return obj
	},
	create(base) {
		return exports.StartScanRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseStartScanRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? common_1.Metadata.fromPartial(object.metadata) : undefined
		message.workspacePath = object.workspacePath ?? ""
		message.options =
			object.options !== undefined && object.options !== null ? exports.ScanOptions.fromPartial(object.options) : undefined
		return message
	},
}
function createBaseScanProgressResponse() {
	return { progress: undefined }
}
exports.ScanProgressResponse = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.progress !== undefined) {
			exports.ScanProgress.encode(message.progress, writer.uint32(10).fork()).join()
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseScanProgressResponse()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.progress = exports.ScanProgress.decode(reader, reader.uint32())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return { progress: isSet(object.progress) ? exports.ScanProgress.fromJSON(object.progress) : undefined }
	},
	toJSON(message) {
		const obj = {}
		if (message.progress !== undefined) {
			obj.progress = exports.ScanProgress.toJSON(message.progress)
		}
		return obj
	},
	create(base) {
		return exports.ScanProgressResponse.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseScanProgressResponse()
		message.progress =
			object.progress !== undefined && object.progress !== null
				? exports.ScanProgress.fromPartial(object.progress)
				: undefined
		return message
	},
}
function createBaseDatabaseStatusResponse() {
	return { status: undefined }
}
exports.DatabaseStatusResponse = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.status !== undefined) {
			exports.DatabaseStatus.encode(message.status, writer.uint32(10).fork()).join()
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseDatabaseStatusResponse()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.status = exports.DatabaseStatus.decode(reader, reader.uint32())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return { status: isSet(object.status) ? exports.DatabaseStatus.fromJSON(object.status) : undefined }
	},
	toJSON(message) {
		const obj = {}
		if (message.status !== undefined) {
			obj.status = exports.DatabaseStatus.toJSON(message.status)
		}
		return obj
	},
	create(base) {
		return exports.DatabaseStatusResponse.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseDatabaseStatusResponse()
		message.status =
			object.status !== undefined && object.status !== null ? exports.DatabaseStatus.fromPartial(object.status) : undefined
		return message
	},
}
function createBaseSearchDefinitionsRequest() {
	return { metadata: undefined, symbolName: "", limit: undefined }
}
exports.SearchDefinitionsRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			common_1.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		if (message.symbolName !== "") {
			writer.uint32(18).string(message.symbolName)
		}
		if (message.limit !== undefined) {
			writer.uint32(24).int32(message.limit)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseSearchDefinitionsRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = common_1.Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.symbolName = reader.string()
					continue
				}
				case 3: {
					if (tag !== 24) {
						break
					}
					message.limit = reader.int32()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			metadata: isSet(object.metadata) ? common_1.Metadata.fromJSON(object.metadata) : undefined,
			symbolName: isSet(object.symbolName) ? globalThis.String(object.symbolName) : "",
			limit: isSet(object.limit) ? globalThis.Number(object.limit) : undefined,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = common_1.Metadata.toJSON(message.metadata)
		}
		if (message.symbolName !== "") {
			obj.symbolName = message.symbolName
		}
		if (message.limit !== undefined) {
			obj.limit = Math.round(message.limit)
		}
		return obj
	},
	create(base) {
		return exports.SearchDefinitionsRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseSearchDefinitionsRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? common_1.Metadata.fromPartial(object.metadata) : undefined
		message.symbolName = object.symbolName ?? ""
		message.limit = object.limit ?? undefined
		return message
	},
}
function createBaseAstUsage() {
	return { targetsForGuesswork: [], resolvedAs: "", debugHint: "", uline: 0 }
}
exports.AstUsage = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		for (const v of message.targetsForGuesswork) {
			writer.uint32(10).string(v)
		}
		if (message.resolvedAs !== "") {
			writer.uint32(18).string(message.resolvedAs)
		}
		if (message.debugHint !== "") {
			writer.uint32(26).string(message.debugHint)
		}
		if (message.uline !== 0) {
			writer.uint32(32).int32(message.uline)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseAstUsage()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.targetsForGuesswork.push(reader.string())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.resolvedAs = reader.string()
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}
					message.debugHint = reader.string()
					continue
				}
				case 4: {
					if (tag !== 32) {
						break
					}
					message.uline = reader.int32()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			targetsForGuesswork: globalThis.Array.isArray(object?.targetsForGuesswork)
				? object.targetsForGuesswork.map((e) => globalThis.String(e))
				: [],
			resolvedAs: isSet(object.resolvedAs) ? globalThis.String(object.resolvedAs) : "",
			debugHint: isSet(object.debugHint) ? globalThis.String(object.debugHint) : "",
			uline: isSet(object.uline) ? globalThis.Number(object.uline) : 0,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.targetsForGuesswork?.length) {
			obj.targetsForGuesswork = message.targetsForGuesswork
		}
		if (message.resolvedAs !== "") {
			obj.resolvedAs = message.resolvedAs
		}
		if (message.debugHint !== "") {
			obj.debugHint = message.debugHint
		}
		if (message.uline !== 0) {
			obj.uline = Math.round(message.uline)
		}
		return obj
	},
	create(base) {
		return exports.AstUsage.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseAstUsage()
		message.targetsForGuesswork = object.targetsForGuesswork?.map((e) => e) || []
		message.resolvedAs = object.resolvedAs ?? ""
		message.debugHint = object.debugHint ?? ""
		message.uline = object.uline ?? 0
		return message
	},
}
function createBaseAstDefinition() {
	return {
		officialPath: [],
		symbolType: "",
		resolvedType: "",
		thisIsAClass: "",
		thisClassDerivedFrom: [],
		cpath: "",
		declLine1: 0,
		declLine2: 0,
		bodyLine1: 0,
		bodyLine2: 0,
		usages: [],
	}
}
exports.AstDefinition = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		for (const v of message.officialPath) {
			writer.uint32(10).string(v)
		}
		if (message.symbolType !== "") {
			writer.uint32(18).string(message.symbolType)
		}
		if (message.resolvedType !== "") {
			writer.uint32(26).string(message.resolvedType)
		}
		if (message.thisIsAClass !== "") {
			writer.uint32(34).string(message.thisIsAClass)
		}
		for (const v of message.thisClassDerivedFrom) {
			writer.uint32(42).string(v)
		}
		if (message.cpath !== "") {
			writer.uint32(50).string(message.cpath)
		}
		if (message.declLine1 !== 0) {
			writer.uint32(56).int32(message.declLine1)
		}
		if (message.declLine2 !== 0) {
			writer.uint32(64).int32(message.declLine2)
		}
		if (message.bodyLine1 !== 0) {
			writer.uint32(72).int32(message.bodyLine1)
		}
		if (message.bodyLine2 !== 0) {
			writer.uint32(80).int32(message.bodyLine2)
		}
		for (const v of message.usages) {
			exports.AstUsage.encode(v, writer.uint32(90).fork()).join()
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseAstDefinition()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.officialPath.push(reader.string())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.symbolType = reader.string()
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}
					message.resolvedType = reader.string()
					continue
				}
				case 4: {
					if (tag !== 34) {
						break
					}
					message.thisIsAClass = reader.string()
					continue
				}
				case 5: {
					if (tag !== 42) {
						break
					}
					message.thisClassDerivedFrom.push(reader.string())
					continue
				}
				case 6: {
					if (tag !== 50) {
						break
					}
					message.cpath = reader.string()
					continue
				}
				case 7: {
					if (tag !== 56) {
						break
					}
					message.declLine1 = reader.int32()
					continue
				}
				case 8: {
					if (tag !== 64) {
						break
					}
					message.declLine2 = reader.int32()
					continue
				}
				case 9: {
					if (tag !== 72) {
						break
					}
					message.bodyLine1 = reader.int32()
					continue
				}
				case 10: {
					if (tag !== 80) {
						break
					}
					message.bodyLine2 = reader.int32()
					continue
				}
				case 11: {
					if (tag !== 90) {
						break
					}
					message.usages.push(exports.AstUsage.decode(reader, reader.uint32()))
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			officialPath: globalThis.Array.isArray(object?.officialPath)
				? object.officialPath.map((e) => globalThis.String(e))
				: [],
			symbolType: isSet(object.symbolType) ? globalThis.String(object.symbolType) : "",
			resolvedType: isSet(object.resolvedType) ? globalThis.String(object.resolvedType) : "",
			thisIsAClass: isSet(object.thisIsAClass) ? globalThis.String(object.thisIsAClass) : "",
			thisClassDerivedFrom: globalThis.Array.isArray(object?.thisClassDerivedFrom)
				? object.thisClassDerivedFrom.map((e) => globalThis.String(e))
				: [],
			cpath: isSet(object.cpath) ? globalThis.String(object.cpath) : "",
			declLine1: isSet(object.declLine1) ? globalThis.Number(object.declLine1) : 0,
			declLine2: isSet(object.declLine2) ? globalThis.Number(object.declLine2) : 0,
			bodyLine1: isSet(object.bodyLine1) ? globalThis.Number(object.bodyLine1) : 0,
			bodyLine2: isSet(object.bodyLine2) ? globalThis.Number(object.bodyLine2) : 0,
			usages: globalThis.Array.isArray(object?.usages) ? object.usages.map((e) => exports.AstUsage.fromJSON(e)) : [],
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.officialPath?.length) {
			obj.officialPath = message.officialPath
		}
		if (message.symbolType !== "") {
			obj.symbolType = message.symbolType
		}
		if (message.resolvedType !== "") {
			obj.resolvedType = message.resolvedType
		}
		if (message.thisIsAClass !== "") {
			obj.thisIsAClass = message.thisIsAClass
		}
		if (message.thisClassDerivedFrom?.length) {
			obj.thisClassDerivedFrom = message.thisClassDerivedFrom
		}
		if (message.cpath !== "") {
			obj.cpath = message.cpath
		}
		if (message.declLine1 !== 0) {
			obj.declLine1 = Math.round(message.declLine1)
		}
		if (message.declLine2 !== 0) {
			obj.declLine2 = Math.round(message.declLine2)
		}
		if (message.bodyLine1 !== 0) {
			obj.bodyLine1 = Math.round(message.bodyLine1)
		}
		if (message.bodyLine2 !== 0) {
			obj.bodyLine2 = Math.round(message.bodyLine2)
		}
		if (message.usages?.length) {
			obj.usages = message.usages.map((e) => exports.AstUsage.toJSON(e))
		}
		return obj
	},
	create(base) {
		return exports.AstDefinition.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseAstDefinition()
		message.officialPath = object.officialPath?.map((e) => e) || []
		message.symbolType = object.symbolType ?? ""
		message.resolvedType = object.resolvedType ?? ""
		message.thisIsAClass = object.thisIsAClass ?? ""
		message.thisClassDerivedFrom = object.thisClassDerivedFrom?.map((e) => e) || []
		message.cpath = object.cpath ?? ""
		message.declLine1 = object.declLine1 ?? 0
		message.declLine2 = object.declLine2 ?? 0
		message.bodyLine1 = object.bodyLine1 ?? 0
		message.bodyLine2 = object.bodyLine2 ?? 0
		message.usages = object.usages?.map((e) => exports.AstUsage.fromPartial(e)) || []
		return message
	},
}
function createBaseSearchDefinitionsResponse() {
	return { definitions: [] }
}
exports.SearchDefinitionsResponse = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		for (const v of message.definitions) {
			exports.AstDefinition.encode(v, writer.uint32(10).fork()).join()
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseSearchDefinitionsResponse()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.definitions.push(exports.AstDefinition.decode(reader, reader.uint32()))
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			definitions: globalThis.Array.isArray(object?.definitions)
				? object.definitions.map((e) => exports.AstDefinition.fromJSON(e))
				: [],
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.definitions?.length) {
			obj.definitions = message.definitions.map((e) => exports.AstDefinition.toJSON(e))
		}
		return obj
	},
	create(base) {
		return exports.SearchDefinitionsResponse.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseSearchDefinitionsResponse()
		message.definitions = object.definitions?.map((e) => exports.AstDefinition.fromPartial(e)) || []
		return message
	},
}
function createBaseGetContextRequest() {
	return { metadata: undefined, filePath: "", line: 0, character: 0, maxTokens: undefined }
}
exports.GetContextRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			common_1.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		if (message.filePath !== "") {
			writer.uint32(18).string(message.filePath)
		}
		if (message.line !== 0) {
			writer.uint32(24).int32(message.line)
		}
		if (message.character !== 0) {
			writer.uint32(32).int32(message.character)
		}
		if (message.maxTokens !== undefined) {
			writer.uint32(40).int32(message.maxTokens)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseGetContextRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = common_1.Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.filePath = reader.string()
					continue
				}
				case 3: {
					if (tag !== 24) {
						break
					}
					message.line = reader.int32()
					continue
				}
				case 4: {
					if (tag !== 32) {
						break
					}
					message.character = reader.int32()
					continue
				}
				case 5: {
					if (tag !== 40) {
						break
					}
					message.maxTokens = reader.int32()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			metadata: isSet(object.metadata) ? common_1.Metadata.fromJSON(object.metadata) : undefined,
			filePath: isSet(object.filePath) ? globalThis.String(object.filePath) : "",
			line: isSet(object.line) ? globalThis.Number(object.line) : 0,
			character: isSet(object.character) ? globalThis.Number(object.character) : 0,
			maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : undefined,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = common_1.Metadata.toJSON(message.metadata)
		}
		if (message.filePath !== "") {
			obj.filePath = message.filePath
		}
		if (message.line !== 0) {
			obj.line = Math.round(message.line)
		}
		if (message.character !== 0) {
			obj.character = Math.round(message.character)
		}
		if (message.maxTokens !== undefined) {
			obj.maxTokens = Math.round(message.maxTokens)
		}
		return obj
	},
	create(base) {
		return exports.GetContextRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseGetContextRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? common_1.Metadata.fromPartial(object.metadata) : undefined
		message.filePath = object.filePath ?? ""
		message.line = object.line ?? 0
		message.character = object.character ?? 0
		message.maxTokens = object.maxTokens ?? undefined
		return message
	},
}
function createBaseContextFile() {
	return { fileName: "", fileContent: "", line1: 0, line2: 0, symbols: [], gradientType: 0, usefulness: 0 }
}
exports.ContextFile = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.fileName !== "") {
			writer.uint32(10).string(message.fileName)
		}
		if (message.fileContent !== "") {
			writer.uint32(18).string(message.fileContent)
		}
		if (message.line1 !== 0) {
			writer.uint32(24).int32(message.line1)
		}
		if (message.line2 !== 0) {
			writer.uint32(32).int32(message.line2)
		}
		for (const v of message.symbols) {
			writer.uint32(42).string(v)
		}
		if (message.gradientType !== 0) {
			writer.uint32(48).int32(message.gradientType)
		}
		if (message.usefulness !== 0) {
			writer.uint32(57).double(message.usefulness)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseContextFile()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.fileName = reader.string()
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.fileContent = reader.string()
					continue
				}
				case 3: {
					if (tag !== 24) {
						break
					}
					message.line1 = reader.int32()
					continue
				}
				case 4: {
					if (tag !== 32) {
						break
					}
					message.line2 = reader.int32()
					continue
				}
				case 5: {
					if (tag !== 42) {
						break
					}
					message.symbols.push(reader.string())
					continue
				}
				case 6: {
					if (tag !== 48) {
						break
					}
					message.gradientType = reader.int32()
					continue
				}
				case 7: {
					if (tag !== 57) {
						break
					}
					message.usefulness = reader.double()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			fileName: isSet(object.fileName) ? globalThis.String(object.fileName) : "",
			fileContent: isSet(object.fileContent) ? globalThis.String(object.fileContent) : "",
			line1: isSet(object.line1) ? globalThis.Number(object.line1) : 0,
			line2: isSet(object.line2) ? globalThis.Number(object.line2) : 0,
			symbols: globalThis.Array.isArray(object?.symbols) ? object.symbols.map((e) => globalThis.String(e)) : [],
			gradientType: isSet(object.gradientType) ? globalThis.Number(object.gradientType) : 0,
			usefulness: isSet(object.usefulness) ? globalThis.Number(object.usefulness) : 0,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.fileName !== "") {
			obj.fileName = message.fileName
		}
		if (message.fileContent !== "") {
			obj.fileContent = message.fileContent
		}
		if (message.line1 !== 0) {
			obj.line1 = Math.round(message.line1)
		}
		if (message.line2 !== 0) {
			obj.line2 = Math.round(message.line2)
		}
		if (message.symbols?.length) {
			obj.symbols = message.symbols
		}
		if (message.gradientType !== 0) {
			obj.gradientType = Math.round(message.gradientType)
		}
		if (message.usefulness !== 0) {
			obj.usefulness = message.usefulness
		}
		return obj
	},
	create(base) {
		return exports.ContextFile.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseContextFile()
		message.fileName = object.fileName ?? ""
		message.fileContent = object.fileContent ?? ""
		message.line1 = object.line1 ?? 0
		message.line2 = object.line2 ?? 0
		message.symbols = object.symbols?.map((e) => e) || []
		message.gradientType = object.gradientType ?? 0
		message.usefulness = object.usefulness ?? 0
		return message
	},
}
function createBaseGetContextResponse() {
	return { contextFiles: [], extraContext: "" }
}
exports.GetContextResponse = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		for (const v of message.contextFiles) {
			exports.ContextFile.encode(v, writer.uint32(10).fork()).join()
		}
		if (message.extraContext !== "") {
			writer.uint32(18).string(message.extraContext)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseGetContextResponse()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.contextFiles.push(exports.ContextFile.decode(reader, reader.uint32()))
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.extraContext = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			contextFiles: globalThis.Array.isArray(object?.contextFiles)
				? object.contextFiles.map((e) => exports.ContextFile.fromJSON(e))
				: [],
			extraContext: isSet(object.extraContext) ? globalThis.String(object.extraContext) : "",
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.contextFiles?.length) {
			obj.contextFiles = message.contextFiles.map((e) => exports.ContextFile.toJSON(e))
		}
		if (message.extraContext !== "") {
			obj.extraContext = message.extraContext
		}
		return obj
	},
	create(base) {
		return exports.GetContextResponse.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseGetContextResponse()
		message.contextFiles = object.contextFiles?.map((e) => exports.ContextFile.fromPartial(e)) || []
		message.extraContext = object.extraContext ?? ""
		return message
	},
}
function createBaseGetRelatedSymbolsRequest() {
	return { metadata: undefined, symbolPath: "", maxResults: undefined }
}
exports.GetRelatedSymbolsRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			common_1.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		if (message.symbolPath !== "") {
			writer.uint32(18).string(message.symbolPath)
		}
		if (message.maxResults !== undefined) {
			writer.uint32(24).int32(message.maxResults)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseGetRelatedSymbolsRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = common_1.Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.symbolPath = reader.string()
					continue
				}
				case 3: {
					if (tag !== 24) {
						break
					}
					message.maxResults = reader.int32()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			metadata: isSet(object.metadata) ? common_1.Metadata.fromJSON(object.metadata) : undefined,
			symbolPath: isSet(object.symbolPath) ? globalThis.String(object.symbolPath) : "",
			maxResults: isSet(object.maxResults) ? globalThis.Number(object.maxResults) : undefined,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = common_1.Metadata.toJSON(message.metadata)
		}
		if (message.symbolPath !== "") {
			obj.symbolPath = message.symbolPath
		}
		if (message.maxResults !== undefined) {
			obj.maxResults = Math.round(message.maxResults)
		}
		return obj
	},
	create(base) {
		return exports.GetRelatedSymbolsRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseGetRelatedSymbolsRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? common_1.Metadata.fromPartial(object.metadata) : undefined
		message.symbolPath = object.symbolPath ?? ""
		message.maxResults = object.maxResults ?? undefined
		return message
	},
}
function createBaseGetRelatedSymbolsResponse() {
	return { symbols: [] }
}
exports.GetRelatedSymbolsResponse = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		for (const v of message.symbols) {
			writer.uint32(10).string(v)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseGetRelatedSymbolsResponse()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.symbols.push(reader.string())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			symbols: globalThis.Array.isArray(object?.symbols) ? object.symbols.map((e) => globalThis.String(e)) : [],
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.symbols?.length) {
			obj.symbols = message.symbols
		}
		return obj
	},
	create(base) {
		return exports.GetRelatedSymbolsResponse.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseGetRelatedSymbolsResponse()
		message.symbols = object.symbols?.map((e) => e) || []
		return message
	},
}
function createBaseClearDatabaseRequest() {
	return { metadata: undefined }
}
exports.ClearDatabaseRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			common_1.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseClearDatabaseRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = common_1.Metadata.decode(reader, reader.uint32())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return { metadata: isSet(object.metadata) ? common_1.Metadata.fromJSON(object.metadata) : undefined }
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = common_1.Metadata.toJSON(message.metadata)
		}
		return obj
	},
	create(base) {
		return exports.ClearDatabaseRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseClearDatabaseRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? common_1.Metadata.fromPartial(object.metadata) : undefined
		return message
	},
}
function createBaseGetStatisticsRequest() {
	return { metadata: undefined }
}
exports.GetStatisticsRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			common_1.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseGetStatisticsRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = common_1.Metadata.decode(reader, reader.uint32())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return { metadata: isSet(object.metadata) ? common_1.Metadata.fromJSON(object.metadata) : undefined }
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = common_1.Metadata.toJSON(message.metadata)
		}
		return obj
	},
	create(base) {
		return exports.GetStatisticsRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseGetStatisticsRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? common_1.Metadata.fromPartial(object.metadata) : undefined
		return message
	},
}
function createBaseFileDefinitionCount() {
	return { file: "", count: 0 }
}
exports.FileDefinitionCount = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.file !== "") {
			writer.uint32(10).string(message.file)
		}
		if (message.count !== 0) {
			writer.uint32(16).int32(message.count)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseFileDefinitionCount()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.file = reader.string()
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}
					message.count = reader.int32()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			file: isSet(object.file) ? globalThis.String(object.file) : "",
			count: isSet(object.count) ? globalThis.Number(object.count) : 0,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.file !== "") {
			obj.file = message.file
		}
		if (message.count !== 0) {
			obj.count = Math.round(message.count)
		}
		return obj
	},
	create(base) {
		return exports.FileDefinitionCount.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseFileDefinitionCount()
		message.file = object.file ?? ""
		message.count = object.count ?? 0
		return message
	},
}
function createBaseDatabaseStatistics() {
	return { totalDefinitions: 0, totalUsages: 0, totalFiles: 0, definitionsByType: {}, filesWithMostDefinitions: [] }
}
exports.DatabaseStatistics = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.totalDefinitions !== 0) {
			writer.uint32(8).int32(message.totalDefinitions)
		}
		if (message.totalUsages !== 0) {
			writer.uint32(16).int32(message.totalUsages)
		}
		if (message.totalFiles !== 0) {
			writer.uint32(24).int32(message.totalFiles)
		}
		Object.entries(message.definitionsByType).forEach(([key, value]) => {
			exports.DatabaseStatistics_DefinitionsByTypeEntry.encode({ key: key, value }, writer.uint32(34).fork()).join()
		})
		for (const v of message.filesWithMostDefinitions) {
			exports.FileDefinitionCount.encode(v, writer.uint32(42).fork()).join()
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseDatabaseStatistics()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 8) {
						break
					}
					message.totalDefinitions = reader.int32()
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}
					message.totalUsages = reader.int32()
					continue
				}
				case 3: {
					if (tag !== 24) {
						break
					}
					message.totalFiles = reader.int32()
					continue
				}
				case 4: {
					if (tag !== 34) {
						break
					}
					const entry4 = exports.DatabaseStatistics_DefinitionsByTypeEntry.decode(reader, reader.uint32())
					if (entry4.value !== undefined) {
						message.definitionsByType[entry4.key] = entry4.value
					}
					continue
				}
				case 5: {
					if (tag !== 42) {
						break
					}
					message.filesWithMostDefinitions.push(exports.FileDefinitionCount.decode(reader, reader.uint32()))
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			totalDefinitions: isSet(object.totalDefinitions) ? globalThis.Number(object.totalDefinitions) : 0,
			totalUsages: isSet(object.totalUsages) ? globalThis.Number(object.totalUsages) : 0,
			totalFiles: isSet(object.totalFiles) ? globalThis.Number(object.totalFiles) : 0,
			definitionsByType: isObject(object.definitionsByType)
				? Object.entries(object.definitionsByType).reduce((acc, [key, value]) => {
						acc[key] = Number(value)
						return acc
					}, {})
				: {},
			filesWithMostDefinitions: globalThis.Array.isArray(object?.filesWithMostDefinitions)
				? object.filesWithMostDefinitions.map((e) => exports.FileDefinitionCount.fromJSON(e))
				: [],
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.totalDefinitions !== 0) {
			obj.totalDefinitions = Math.round(message.totalDefinitions)
		}
		if (message.totalUsages !== 0) {
			obj.totalUsages = Math.round(message.totalUsages)
		}
		if (message.totalFiles !== 0) {
			obj.totalFiles = Math.round(message.totalFiles)
		}
		if (message.definitionsByType) {
			const entries = Object.entries(message.definitionsByType)
			if (entries.length > 0) {
				obj.definitionsByType = {}
				entries.forEach(([k, v]) => {
					obj.definitionsByType[k] = Math.round(v)
				})
			}
		}
		if (message.filesWithMostDefinitions?.length) {
			obj.filesWithMostDefinitions = message.filesWithMostDefinitions.map((e) => exports.FileDefinitionCount.toJSON(e))
		}
		return obj
	},
	create(base) {
		return exports.DatabaseStatistics.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseDatabaseStatistics()
		message.totalDefinitions = object.totalDefinitions ?? 0
		message.totalUsages = object.totalUsages ?? 0
		message.totalFiles = object.totalFiles ?? 0
		message.definitionsByType = Object.entries(object.definitionsByType ?? {}).reduce((acc, [key, value]) => {
			if (value !== undefined) {
				acc[key] = globalThis.Number(value)
			}
			return acc
		}, {})
		message.filesWithMostDefinitions =
			object.filesWithMostDefinitions?.map((e) => exports.FileDefinitionCount.fromPartial(e)) || []
		return message
	},
}
function createBaseDatabaseStatistics_DefinitionsByTypeEntry() {
	return { key: "", value: 0 }
}
exports.DatabaseStatistics_DefinitionsByTypeEntry = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.key !== "") {
			writer.uint32(10).string(message.key)
		}
		if (message.value !== 0) {
			writer.uint32(16).int32(message.value)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseDatabaseStatistics_DefinitionsByTypeEntry()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.key = reader.string()
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}
					message.value = reader.int32()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			key: isSet(object.key) ? globalThis.String(object.key) : "",
			value: isSet(object.value) ? globalThis.Number(object.value) : 0,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.key !== "") {
			obj.key = message.key
		}
		if (message.value !== 0) {
			obj.value = Math.round(message.value)
		}
		return obj
	},
	create(base) {
		return exports.DatabaseStatistics_DefinitionsByTypeEntry.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseDatabaseStatistics_DefinitionsByTypeEntry()
		message.key = object.key ?? ""
		message.value = object.value ?? 0
		return message
	},
}
function createBaseGetStatisticsResponse() {
	return { statistics: undefined }
}
exports.GetStatisticsResponse = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.statistics !== undefined) {
			exports.DatabaseStatistics.encode(message.statistics, writer.uint32(10).fork()).join()
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseGetStatisticsResponse()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.statistics = exports.DatabaseStatistics.decode(reader, reader.uint32())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return { statistics: isSet(object.statistics) ? exports.DatabaseStatistics.fromJSON(object.statistics) : undefined }
	},
	toJSON(message) {
		const obj = {}
		if (message.statistics !== undefined) {
			obj.statistics = exports.DatabaseStatistics.toJSON(message.statistics)
		}
		return obj
	},
	create(base) {
		return exports.GetStatisticsResponse.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseGetStatisticsResponse()
		message.statistics =
			object.statistics !== undefined && object.statistics !== null
				? exports.DatabaseStatistics.fromPartial(object.statistics)
				: undefined
		return message
	},
}
exports.AstDatabaseServiceDefinition = {
	name: "AstDatabaseService",
	fullName: "cline.AstDatabaseService",
	methods: {
		getDatabaseStatus: {
			name: "GetDatabaseStatus",
			requestType: common_1.EmptyRequest,
			requestStream: false,
			responseType: exports.DatabaseStatusResponse,
			responseStream: false,
			options: {},
		},
		startWorkspaceScan: {
			name: "StartWorkspaceScan",
			requestType: exports.StartScanRequest,
			requestStream: false,
			responseType: common_1.Empty,
			responseStream: false,
			options: {},
		},
		getScanProgress: {
			name: "GetScanProgress",
			requestType: common_1.EmptyRequest,
			requestStream: false,
			responseType: exports.ScanProgressResponse,
			responseStream: false,
			options: {},
		},
		searchDefinitions: {
			name: "SearchDefinitions",
			requestType: exports.SearchDefinitionsRequest,
			requestStream: false,
			responseType: exports.SearchDefinitionsResponse,
			responseStream: false,
			options: {},
		},
		getContext: {
			name: "GetContext",
			requestType: exports.GetContextRequest,
			requestStream: false,
			responseType: exports.GetContextResponse,
			responseStream: false,
			options: {},
		},
		getRelatedSymbols: {
			name: "GetRelatedSymbols",
			requestType: exports.GetRelatedSymbolsRequest,
			requestStream: false,
			responseType: exports.GetRelatedSymbolsResponse,
			responseStream: false,
			options: {},
		},
		getStatistics: {
			name: "GetStatistics",
			requestType: exports.GetStatisticsRequest,
			requestStream: false,
			responseType: exports.GetStatisticsResponse,
			responseStream: false,
			options: {},
		},
		clearDatabase: {
			name: "ClearDatabase",
			requestType: exports.ClearDatabaseRequest,
			requestStream: false,
			responseType: common_1.Empty,
			responseStream: false,
			options: {},
		},
	},
}
function longToNumber(int64) {
	const num = globalThis.Number(int64.toString())
	if (num > globalThis.Number.MAX_SAFE_INTEGER) {
		throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER")
	}
	if (num < globalThis.Number.MIN_SAFE_INTEGER) {
		throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER")
	}
	return num
}
function isObject(value) {
	return typeof value === "object" && value !== null
}
function isSet(value) {
	return value !== null && value !== undefined
}
