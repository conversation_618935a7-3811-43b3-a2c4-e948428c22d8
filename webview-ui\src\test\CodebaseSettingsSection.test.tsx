/**
 * Frontend Tests for CodebaseSettingsSection
 * Tests the UI components and real progress monitoring
 */

import React from "react"
import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import "@testing-library/jest-dom"
import CodebaseSettingsSection from "../components/settings/CodebaseSettingsSection"
import { ExtensionStateContextProvider } from "../context/ExtensionStateContext"

// Mock the gRPC client
const mockGetDatabaseStatus = jest.fn()
const mockStartWorkspaceScan = jest.fn()
const mockGetScanProgress = jest.fn()

jest.mock("../services/grpc-client", () => ({
	AstDatabaseServiceClient: {
		getDatabaseStatus: mockGetDatabaseStatus,
		startWorkspaceScan: mockStartWorkspaceScan,
		getScanProgress: mockGetScanProgress,
	},
}))

const mockExtensionState = {
	// Add all required state properties
	apiConfiguration: null,
	telemetrySetting: "enabled" as const,
	showAnnouncement: false,
	shouldShowAnnouncement: false,
	planActSeparateModelsSetting: false,
	enableCheckpointsSetting: false,
	mcpMarketplaceEnabled: false,
	mcpRichDisplayEnabled: false,
	mcpResponsesCollapsed: false,
	shellIntegrationTimeout: 5000,
	terminalReuseEnabled: true,
	terminalOutputLineLimit: 500,
	defaultTerminalProfile: "default",
	isNewUser: false,
	version: "1.0.0",
	distinctId: "test-id",
	astDatabaseAutoScan: false,
	astDatabaseMaxFiles: 1000,
	astDatabaseIncludeExtensions: ["js", "ts"],
	astDatabaseExcludePatterns: ["node_modules/**"],
	// Add setter functions
	setApiConfiguration: jest.fn(),
	setTelemetrySetting: jest.fn(),
	setShowAnnouncement: jest.fn(),
	setShouldShowAnnouncement: jest.fn(),
	setPlanActSeparateModelsSetting: jest.fn(),
	setEnableCheckpointsSetting: jest.fn(),
	setMcpMarketplaceEnabled: jest.fn(),
	setMcpRichDisplayEnabled: jest.fn(),
	setMcpResponsesCollapsed: jest.fn(),
	setShellIntegrationTimeout: jest.fn(),
	setTerminalReuseEnabled: jest.fn(),
	setTerminalOutputLineLimit: jest.fn(),
	setDefaultTerminalProfile: jest.fn(),
	setAstDatabaseAutoScan: jest.fn(),
	setAstDatabaseMaxFiles: jest.fn(),
	setAstDatabaseIncludeExtensions: jest.fn(),
	setAstDatabaseExcludePatterns: jest.fn(),
}

const MockExtensionStateProvider = ({ children }: { children: React.ReactNode }) => {
	return <ExtensionStateContextProvider>{children}</ExtensionStateContextProvider>
}

describe("CodebaseSettingsSection", () => {
	beforeEach(() => {
		jest.clearAllMocks()
	})

	it("should render database status correctly", async () => {
		mockGetDatabaseStatus.mockResolvedValue({
			status: {
				astate: "ready",
				astIndexFilesTotal: 150,
				astIndexSymbolsTotal: 1259,
				astIndexUsagesTotal: 3444,
				lastUpdated: new Date().toISOString(),
			},
		})

		render(
			<MockExtensionStateProvider>
				<CodebaseSettingsSection />
			</MockExtensionStateProvider>,
		)

		await waitFor(() => {
			expect(screen.getByText("Status: Ready")).toBeInTheDocument()
		})

		expect(screen.getByText("150")).toBeInTheDocument() // Files Indexed
		expect(screen.getByText("1259")).toBeInTheDocument() // Definitions
		expect(screen.getByText("3444")).toBeInTheDocument() // Usages
	})

	it("should start real scan when rescan button is clicked", async () => {
		mockGetDatabaseStatus.mockResolvedValue({
			status: { astate: "ready" },
		})
		mockStartWorkspaceScan.mockResolvedValue({})
		mockGetScanProgress.mockResolvedValue({
			progress: {
				totalFiles: 100,
				processedFiles: 0,
				currentFile: "Initializing scan...",
				status: "scanning",
				startTime: new Date().toISOString(),
				elapsedMs: 0,
				estimatedRemainingMs: 0,
				filesPerSecond: 0,
				definitionsFound: 0,
				usagesFound: 0,
				bytesProcessed: 0,
				errors: [],
			},
		})

		render(
			<MockExtensionStateProvider>
				<CodebaseSettingsSection />
			</MockExtensionStateProvider>,
		)

		const rescanButton = screen.getByText("Rescan Workspace")
		fireEvent.click(rescanButton)

		await waitFor(() => {
			expect(mockStartWorkspaceScan).toHaveBeenCalled()
		})

		// Verify progress monitoring starts
		await waitFor(
			() => {
				expect(mockGetScanProgress).toHaveBeenCalled()
			},
			{ timeout: 1000 },
		)
	})

	it("should display progress correctly during scan", async () => {
		// Mock progressive scan updates
		let callCount = 0
		mockGetScanProgress.mockImplementation(() => {
			callCount++
			if (callCount === 1) {
				return Promise.resolve({
					progress: {
						totalFiles: 100,
						processedFiles: 0,
						currentFile: "Initializing scan...",
						status: "scanning",
						startTime: new Date().toISOString(),
						elapsedMs: 0,
						estimatedRemainingMs: 5000,
						filesPerSecond: 0,
						definitionsFound: 0,
						usagesFound: 0,
						bytesProcessed: 0,
						errors: [],
					},
				})
			} else if (callCount === 2) {
				return Promise.resolve({
					progress: {
						totalFiles: 100,
						processedFiles: 50,
						currentFile: "src/main.ts",
						status: "parsing",
						startTime: new Date().toISOString(),
						elapsedMs: 2500,
						estimatedRemainingMs: 2500,
						filesPerSecond: 20,
						definitionsFound: 25,
						usagesFound: 75,
						bytesProcessed: 50000,
						errors: [],
					},
				})
			} else {
				return Promise.resolve({
					progress: {
						totalFiles: 100,
						processedFiles: 100,
						currentFile: "",
						status: "complete",
						startTime: new Date().toISOString(),
						elapsedMs: 5000,
						estimatedRemainingMs: 0,
						filesPerSecond: 20,
						definitionsFound: 50,
						usagesFound: 150,
						bytesProcessed: 100000,
						errors: [],
					},
				})
			}
		})

		mockGetDatabaseStatus.mockResolvedValue({
			status: { astate: "ready" },
		})
		mockStartWorkspaceScan.mockResolvedValue({})

		render(
			<MockExtensionStateProvider>
				<CodebaseSettingsSection />
			</MockExtensionStateProvider>,
		)

		const rescanButton = screen.getByText("Rescan Workspace")
		fireEvent.click(rescanButton)

		// Wait for initial progress
		await waitFor(() => {
			expect(screen.getByText("0%")).toBeInTheDocument()
		})

		// Wait for mid-progress
		await waitFor(
			() => {
				expect(screen.getByText("50%")).toBeInTheDocument()
			},
			{ timeout: 2000 },
		)

		// Wait for completion
		await waitFor(
			() => {
				expect(screen.getByText("100%")).toBeInTheDocument()
			},
			{ timeout: 3000 },
		)
	})

	it("should handle progress calculation edge cases", () => {
		// Test progress percentage calculation
		const calculateProgress = (processed: number, total: number) => {
			return total > 0 ? Math.round((processed / total) * 100) : 0
		}

		expect(calculateProgress(0, 100)).toBe(0)
		expect(calculateProgress(50, 100)).toBe(50)
		expect(calculateProgress(100, 100)).toBe(100)
		expect(calculateProgress(0, 0)).toBe(0) // Edge case
		expect(calculateProgress(10, 0)).toBe(0) // Edge case
	})

	it("should handle null progress gracefully", () => {
		const progress: any = null

		// Test safe access patterns
		const processedFiles = progress?.processedFiles || 0
		const totalFiles = progress?.totalFiles || 0
		const currentFile = progress?.currentFile || ""
		const filesPerSecond = progress?.filesPerSecond || 0
		const definitionsFound = progress?.definitionsFound || 0
		const usagesFound = progress?.usagesFound || 0

		expect(processedFiles).toBe(0)
		expect(totalFiles).toBe(0)
		expect(currentFile).toBe("")
		expect(filesPerSecond).toBe(0)
		expect(definitionsFound).toBe(0)
		expect(usagesFound).toBe(0)
	})
})
