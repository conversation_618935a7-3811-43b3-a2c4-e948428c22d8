/**
 * AST Database gRPC Service
 * Provides gRPC endpoints for AST database operations
 */

import { AstDatabaseService } from "./AstDatabaseService";
import { AstDatabaseContextService } from "./AstDatabaseContextService";
import {
  Empty,
  EmptyRequest,
} from "@shared/proto/common";
import {
  DatabaseStatusResponse,
  ScanProgressResponse,
  StartScanRequest,
  SearchDefinitionsRequest,
  SearchDefinitionsResponse,
  GetContextRequest,
  GetContextResponse,
  GetRelatedSymbolsRequest,
  GetRelatedSymbolsResponse,
  GetStatisticsRequest,
  GetStatisticsResponse,
  ClearDatabaseRequest,
} from "@shared/proto/astdb";
import { createLogger, LogLevel } from "@services/astdb";

/**
 * AST Database gRPC Service implementation
 */
export class AstDatabaseGrpcService {
  private logger = createLogger("AstDatabaseGrpcService", LogLevel.INFO);
  private astService: AstDatabaseService;
  private contextService: AstDatabaseContextService;

  constructor() {
    this.astService = AstDatabaseService.getInstance();
    this.contextService = AstDatabaseContextService.getInstance();
    this.logger.info("AstDatabaseGrpcService initialized");
  }

  /**
   * Get database status
   */
  async getDatabaseStatus(request: EmptyRequest): Promise<DatabaseStatusResponse> {
    this.logger.debug("gRPC: getDatabaseStatus");
    return await this.astService.getDatabaseStatus(request);
  }

  /**
   * Start workspace scan
   */
  async startWorkspaceScan(request: StartScanRequest): Promise<Empty> {
    this.logger.debug("gRPC: startWorkspaceScan", { options: request.options });
    return await this.astService.startWorkspaceScan(request);
  }

  /**
   * Get scan progress
   */
  async getScanProgress(request: EmptyRequest): Promise<ScanProgressResponse> {
    this.logger.debug("gRPC: getScanProgress");
    return await this.astService.getScanProgress(request);
  }

  /**
   * Search definitions
   */
  async searchDefinitions(request: SearchDefinitionsRequest): Promise<SearchDefinitionsResponse> {
    this.logger.debug("gRPC: searchDefinitions", {
      symbolName: request.symbolName,
      limit: request.limit,
    });
    return await this.astService.searchDefinitions(request);
  }

  /**
   * Get context for autocomplete
   */
  async getContext(request: GetContextRequest): Promise<GetContextResponse> {
    this.logger.debug("gRPC: getContext", {
      filePath: request.filePath,
      line: request.line,
      character: request.character,
    });
    return await this.contextService.getContext(request);
  }

  /**
   * Get related symbols
   */
  async getRelatedSymbols(request: GetRelatedSymbolsRequest): Promise<GetRelatedSymbolsResponse> {
    this.logger.debug("gRPC: getRelatedSymbols", {
      symbolPath: request.symbolPath,
      maxResults: request.maxResults,
    });
    return await this.contextService.getRelatedSymbols(request);
  }

  /**
   * Get database statistics
   */
  async getStatistics(request: GetStatisticsRequest): Promise<GetStatisticsResponse> {
    this.logger.debug("gRPC: getStatistics");
    return await this.contextService.getStatistics(request);
  }

  /**
   * Clear database
   */
  async clearDatabase(request: ClearDatabaseRequest): Promise<Empty> {
    this.logger.debug("gRPC: clearDatabase");
    // This method needs to be implemented in AstDatabaseService
    throw new Error("clearDatabase method not implemented");
  }

  /**
   * Initialize service with workspace path
   */
  async initialize(workspacePath?: string): Promise<void> {
    this.logger.info("Initializing AstDatabaseGrpcService", { workspacePath });
    await this.astService.initialize(workspacePath);
  }

  /**
   * Dispose of resources
   */
  async dispose(): Promise<void> {
    this.logger.info("Disposing AstDatabaseGrpcService");
    // This method needs to be implemented in AstDatabaseService
    throw new Error("dispose method not implemented");
  }
}
