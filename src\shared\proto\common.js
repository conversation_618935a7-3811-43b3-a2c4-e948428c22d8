"use strict"
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: common.proto
Object.defineProperty(exports, "__esModule", { value: true })
exports.KeyValuePair =
	exports.StringArrays =
	exports.StringArray =
	exports.Boolean =
	exports.BooleanRequest =
	exports.Bytes =
	exports.BytesRequest =
	exports.Int64 =
	exports.Int64Request =
	exports.String =
	exports.StringArrayRequest =
	exports.StringRequest =
	exports.Empty =
	exports.EmptyRequest =
	exports.Metadata =
		void 0
/* eslint-disable */
const wire_1 = require("@bufbuild/protobuf/wire")
function createBaseMetadata() {
	return {}
}
exports.Metadata = {
	encode(_, writer = new wire_1.BinaryWriter()) {
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseMetadata()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(_) {
		return {}
	},
	toJSON(_) {
		const obj = {}
		return obj
	},
	create(base) {
		return exports.Metadata.fromPartial(base ?? {})
	},
	fromPartial(_) {
		const message = createBaseMetadata()
		return message
	},
}
function createBaseEmptyRequest() {
	return { metadata: undefined }
}
exports.EmptyRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			exports.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseEmptyRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = exports.Metadata.decode(reader, reader.uint32())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return { metadata: isSet(object.metadata) ? exports.Metadata.fromJSON(object.metadata) : undefined }
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = exports.Metadata.toJSON(message.metadata)
		}
		return obj
	},
	create(base) {
		return exports.EmptyRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseEmptyRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? exports.Metadata.fromPartial(object.metadata) : undefined
		return message
	},
}
function createBaseEmpty() {
	return {}
}
exports.Empty = {
	encode(_, writer = new wire_1.BinaryWriter()) {
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseEmpty()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(_) {
		return {}
	},
	toJSON(_) {
		const obj = {}
		return obj
	},
	create(base) {
		return exports.Empty.fromPartial(base ?? {})
	},
	fromPartial(_) {
		const message = createBaseEmpty()
		return message
	},
}
function createBaseStringRequest() {
	return { metadata: undefined, value: "" }
}
exports.StringRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			exports.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		if (message.value !== "") {
			writer.uint32(18).string(message.value)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseStringRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = exports.Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.value = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			metadata: isSet(object.metadata) ? exports.Metadata.fromJSON(object.metadata) : undefined,
			value: isSet(object.value) ? globalThis.String(object.value) : "",
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = exports.Metadata.toJSON(message.metadata)
		}
		if (message.value !== "") {
			obj.value = message.value
		}
		return obj
	},
	create(base) {
		return exports.StringRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseStringRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? exports.Metadata.fromPartial(object.metadata) : undefined
		message.value = object.value ?? ""
		return message
	},
}
function createBaseStringArrayRequest() {
	return { metadata: undefined, value: [] }
}
exports.StringArrayRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			exports.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		for (const v of message.value) {
			writer.uint32(18).string(v)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseStringArrayRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = exports.Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.value.push(reader.string())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			metadata: isSet(object.metadata) ? exports.Metadata.fromJSON(object.metadata) : undefined,
			value: globalThis.Array.isArray(object?.value) ? object.value.map((e) => globalThis.String(e)) : [],
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = exports.Metadata.toJSON(message.metadata)
		}
		if (message.value?.length) {
			obj.value = message.value
		}
		return obj
	},
	create(base) {
		return exports.StringArrayRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseStringArrayRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? exports.Metadata.fromPartial(object.metadata) : undefined
		message.value = object.value?.map((e) => e) || []
		return message
	},
}
function createBaseString() {
	return { value: "" }
}
exports.String = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.value !== "") {
			writer.uint32(10).string(message.value)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseString()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.value = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return { value: isSet(object.value) ? globalThis.String(object.value) : "" }
	},
	toJSON(message) {
		const obj = {}
		if (message.value !== "") {
			obj.value = message.value
		}
		return obj
	},
	create(base) {
		return exports.String.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseString()
		message.value = object.value ?? ""
		return message
	},
}
function createBaseInt64Request() {
	return { metadata: undefined, value: 0 }
}
exports.Int64Request = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			exports.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		if (message.value !== 0) {
			writer.uint32(16).int64(message.value)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseInt64Request()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = exports.Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}
					message.value = longToNumber(reader.int64())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			metadata: isSet(object.metadata) ? exports.Metadata.fromJSON(object.metadata) : undefined,
			value: isSet(object.value) ? globalThis.Number(object.value) : 0,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = exports.Metadata.toJSON(message.metadata)
		}
		if (message.value !== 0) {
			obj.value = Math.round(message.value)
		}
		return obj
	},
	create(base) {
		return exports.Int64Request.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseInt64Request()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? exports.Metadata.fromPartial(object.metadata) : undefined
		message.value = object.value ?? 0
		return message
	},
}
function createBaseInt64() {
	return { value: 0 }
}
exports.Int64 = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.value !== 0) {
			writer.uint32(8).int64(message.value)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseInt64()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 8) {
						break
					}
					message.value = longToNumber(reader.int64())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return { value: isSet(object.value) ? globalThis.Number(object.value) : 0 }
	},
	toJSON(message) {
		const obj = {}
		if (message.value !== 0) {
			obj.value = Math.round(message.value)
		}
		return obj
	},
	create(base) {
		return exports.Int64.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseInt64()
		message.value = object.value ?? 0
		return message
	},
}
function createBaseBytesRequest() {
	return { metadata: undefined, value: Buffer.alloc(0) }
}
exports.BytesRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			exports.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		if (message.value.length !== 0) {
			writer.uint32(18).bytes(message.value)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseBytesRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = exports.Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.value = Buffer.from(reader.bytes())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			metadata: isSet(object.metadata) ? exports.Metadata.fromJSON(object.metadata) : undefined,
			value: isSet(object.value) ? Buffer.from(bytesFromBase64(object.value)) : Buffer.alloc(0),
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = exports.Metadata.toJSON(message.metadata)
		}
		if (message.value.length !== 0) {
			obj.value = base64FromBytes(message.value)
		}
		return obj
	},
	create(base) {
		return exports.BytesRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseBytesRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? exports.Metadata.fromPartial(object.metadata) : undefined
		message.value = object.value ?? Buffer.alloc(0)
		return message
	},
}
function createBaseBytes() {
	return { value: Buffer.alloc(0) }
}
exports.Bytes = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.value.length !== 0) {
			writer.uint32(10).bytes(message.value)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseBytes()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.value = Buffer.from(reader.bytes())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return { value: isSet(object.value) ? Buffer.from(bytesFromBase64(object.value)) : Buffer.alloc(0) }
	},
	toJSON(message) {
		const obj = {}
		if (message.value.length !== 0) {
			obj.value = base64FromBytes(message.value)
		}
		return obj
	},
	create(base) {
		return exports.Bytes.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseBytes()
		message.value = object.value ?? Buffer.alloc(0)
		return message
	},
}
function createBaseBooleanRequest() {
	return { metadata: undefined, value: false }
}
exports.BooleanRequest = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.metadata !== undefined) {
			exports.Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		if (message.value !== false) {
			writer.uint32(16).bool(message.value)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseBooleanRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.metadata = exports.Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}
					message.value = reader.bool()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			metadata: isSet(object.metadata) ? exports.Metadata.fromJSON(object.metadata) : undefined,
			value: isSet(object.value) ? globalThis.Boolean(object.value) : false,
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.metadata !== undefined) {
			obj.metadata = exports.Metadata.toJSON(message.metadata)
		}
		if (message.value !== false) {
			obj.value = message.value
		}
		return obj
	},
	create(base) {
		return exports.BooleanRequest.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseBooleanRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? exports.Metadata.fromPartial(object.metadata) : undefined
		message.value = object.value ?? false
		return message
	},
}
function createBaseBoolean() {
	return { value: false }
}
exports.Boolean = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.value !== false) {
			writer.uint32(8).bool(message.value)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseBoolean()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 8) {
						break
					}
					message.value = reader.bool()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return { value: isSet(object.value) ? globalThis.Boolean(object.value) : false }
	},
	toJSON(message) {
		const obj = {}
		if (message.value !== false) {
			obj.value = message.value
		}
		return obj
	},
	create(base) {
		return exports.Boolean.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseBoolean()
		message.value = object.value ?? false
		return message
	},
}
function createBaseStringArray() {
	return { values: [] }
}
exports.StringArray = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		for (const v of message.values) {
			writer.uint32(10).string(v)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseStringArray()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.values.push(reader.string())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			values: globalThis.Array.isArray(object?.values) ? object.values.map((e) => globalThis.String(e)) : [],
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.values?.length) {
			obj.values = message.values
		}
		return obj
	},
	create(base) {
		return exports.StringArray.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseStringArray()
		message.values = object.values?.map((e) => e) || []
		return message
	},
}
function createBaseStringArrays() {
	return { values1: [], values2: [] }
}
exports.StringArrays = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		for (const v of message.values1) {
			writer.uint32(10).string(v)
		}
		for (const v of message.values2) {
			writer.uint32(18).string(v)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseStringArrays()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.values1.push(reader.string())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.values2.push(reader.string())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			values1: globalThis.Array.isArray(object?.values1) ? object.values1.map((e) => globalThis.String(e)) : [],
			values2: globalThis.Array.isArray(object?.values2) ? object.values2.map((e) => globalThis.String(e)) : [],
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.values1?.length) {
			obj.values1 = message.values1
		}
		if (message.values2?.length) {
			obj.values2 = message.values2
		}
		return obj
	},
	create(base) {
		return exports.StringArrays.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseStringArrays()
		message.values1 = object.values1?.map((e) => e) || []
		message.values2 = object.values2?.map((e) => e) || []
		return message
	},
}
function createBaseKeyValuePair() {
	return { key: "", value: "" }
}
exports.KeyValuePair = {
	encode(message, writer = new wire_1.BinaryWriter()) {
		if (message.key !== "") {
			writer.uint32(10).string(message.key)
		}
		if (message.value !== "") {
			writer.uint32(18).string(message.value)
		}
		return writer
	},
	decode(input, length) {
		const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseKeyValuePair()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}
					message.key = reader.string()
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}
					message.value = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},
	fromJSON(object) {
		return {
			key: isSet(object.key) ? globalThis.String(object.key) : "",
			value: isSet(object.value) ? globalThis.String(object.value) : "",
		}
	},
	toJSON(message) {
		const obj = {}
		if (message.key !== "") {
			obj.key = message.key
		}
		if (message.value !== "") {
			obj.value = message.value
		}
		return obj
	},
	create(base) {
		return exports.KeyValuePair.fromPartial(base ?? {})
	},
	fromPartial(object) {
		const message = createBaseKeyValuePair()
		message.key = object.key ?? ""
		message.value = object.value ?? ""
		return message
	},
}
function bytesFromBase64(b64) {
	return Uint8Array.from(globalThis.Buffer.from(b64, "base64"))
}
function base64FromBytes(arr) {
	return globalThis.Buffer.from(arr).toString("base64")
}
function longToNumber(int64) {
	const num = globalThis.Number(int64.toString())
	if (num > globalThis.Number.MAX_SAFE_INTEGER) {
		throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER")
	}
	if (num < globalThis.Number.MIN_SAFE_INTEGER) {
		throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER")
	}
	return num
}
function isSet(value) {
	return value !== null && value !== undefined
}
