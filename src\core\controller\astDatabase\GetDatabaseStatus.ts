/**
 * Get AST Database Status Controller
 */

import { Controller } from ".."
import type { EmptyRequest } from "@shared/proto/common"
import { DatabaseStatusResponse } from "@shared/proto/astdb"
import { AstDatabaseService } from "@services/astdb-service"

export async function GetDatabaseStatus(controller: Controller, request: EmptyRequest): Promise<DatabaseStatusResponse> {
	try {
		// Get the AST database service instance
		const astService = AstDatabaseService.getInstance()

		// Call the service method
		const response = await astService.getDatabaseStatus(request)

		return response
	} catch (error) {
		console.error("Failed to get AST database status:", error)

		// Return empty response if service is not available
		return DatabaseStatusResponse.fromPartial({})
	}
}
