"use strict";
/**
 * Test script for AST Database functionality
 * Run this to verify that the AST database service is working correctly
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.testAstDatabase = testAstDatabase;
const AstDatabaseService_1 = require("./src/services/astdb-service/AstDatabaseService");
const AstDatabaseContextService_1 = require("./src/services/astdb-service/AstDatabaseContextService");
const common_1 = require("./src/shared/proto/common");
const astdb_1 = require("./src/shared/proto/astdb");
async function testAstDatabase() {
    console.log("🧪 Testing AST Database Service...");
    try {
        // Initialize the service
        const astService = AstDatabaseService_1.AstDatabaseService.getInstance();
        const contextService = AstDatabaseContextService_1.AstDatabaseContextService.getInstance();
        console.log("✅ Services initialized successfully");
        // Test 1: Get database status
        console.log("\n📊 Testing database status...");
        const statusResponse = await astService.getDatabaseStatus(common_1.EmptyRequest.fromPartial({}));
        console.log("Database status:", statusResponse.status?.astate || "unknown");
        // Test 2: Start a workspace scan
        console.log("\n🔍 Testing workspace scan...");
        const scanRequest = astdb_1.StartScanRequest.fromPartial({
            workspacePath: process.cwd(),
            options: astdb_1.ScanOptions.fromPartial({
                maxFiles: 100,
                includeExtensions: [".ts", ".js", ".tsx", ".jsx"],
                excludePatterns: ["node_modules", ".git", "dist", "build"]
            })
        });
        await astService.startWorkspaceScan(scanRequest);
        console.log("✅ Workspace scan started");
        // Test 3: Monitor scan progress
        console.log("\n⏳ Monitoring scan progress...");
        let scanComplete = false;
        let attempts = 0;
        const maxAttempts = 30; // 30 seconds timeout
        while (!scanComplete && attempts < maxAttempts) {
            const progressResponse = await astService.getScanProgress(common_1.EmptyRequest.fromPartial({}));
            const progress = progressResponse.progress;
            if (progress) {
                console.log(`Progress: ${progress.processedFiles}/${progress.totalFiles} files (${progress.status})`);
                if (progress.status === "completed" || progress.status === "ready") {
                    scanComplete = true;
                    console.log("✅ Scan completed successfully");
                }
            }
            if (!scanComplete) {
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
                attempts++;
            }
        }
        if (!scanComplete) {
            console.log("⚠️ Scan did not complete within timeout, but this is normal for large codebases");
        }
        // Test 4: Search for definitions
        console.log("\n🔎 Testing definition search...");
        const searchResponse = await astService.searchDefinitions({
            symbolName: "function",
            limit: 5
        });
        console.log(`Found ${searchResponse.definitions.length} function definitions`);
        searchResponse.definitions.slice(0, 3).forEach((def, index) => {
            console.log(`  ${index + 1}. ${def.officialPath.join("::")} (${def.symbolType})`);
        });
        // Test 5: Get database statistics
        console.log("\n📈 Testing database statistics...");
        const statsResponse = await contextService.getStatistics({});
        const stats = statsResponse.statistics;
        if (stats) {
            console.log(`Total definitions: ${stats.totalDefinitions}`);
            console.log(`Total usages: ${stats.totalUsages}`);
            console.log(`Total files: ${stats.totalFiles}`);
            if (stats.filesWithMostDefinitions.length > 0) {
                console.log("Files with most definitions:");
                stats.filesWithMostDefinitions.slice(0, 3).forEach((file, index) => {
                    console.log(`  ${index + 1}. ${file.file}: ${file.count} definitions`);
                });
            }
        }
        // Test 6: Test context retrieval (if we have a TypeScript file)
        console.log("\n🎯 Testing context retrieval...");
        try {
            const contextResponse = await contextService.getContext({
                filePath: __filename,
                line: 10,
                character: 0,
                maxTokens: 500
            });
            console.log(`Retrieved ${contextResponse.contextFiles.length} context files`);
            console.log(`Extra context length: ${contextResponse.extraContext.length} characters`);
        }
        catch (error) {
            console.log("Context retrieval test skipped (file may not be indexed yet)");
        }
        // Test 7: Performance metrics
        console.log("\n📊 Performance metrics...");
        const metrics = astService.getPerformanceMetrics();
        console.log(`Total requests: ${metrics.totalRequests}`);
        console.log(`Success rate: ${metrics.totalRequests > 0 ? ((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1) : 0}%`);
        console.log(`Average response time: ${metrics.averageResponseTime.toFixed(1)}ms`);
        // Test 8: Health status
        console.log("\n🏥 Health status...");
        const health = astService.getHealthStatus();
        console.log(`Status: ${health.status.toUpperCase()} ${health.isHealthy ? "✅" : "❌"}`);
        if (health.issues.length > 0) {
            console.log("Issues:");
            health.issues.forEach(issue => console.log(`  - ${issue}`));
        }
        console.log("\n🎉 All tests completed successfully!");
    }
    catch (error) {
        console.error("❌ Test failed:", error);
        process.exit(1);
    }
}
// Run the test
if (require.main === module) {
    testAstDatabase().catch(console.error);
}
