/**
 * Simple test script for AST Database functionality
 * Run this to verify that the AST database service is working correctly
 */

const path = require('path');

// Mock VSCode API for testing
global.vscode = {
    workspace: {
        workspaceFolders: [{
            uri: { fsPath: __dirname }
        }]
    },
    window: {
        showInformationMessage: console.log,
        showErrorMessage: console.error,
        showWarningMessage: console.warn
    },
    Uri: {
        file: (path) => ({ fsPath: path })
    }
};

async function testAstDatabase() {
    console.log("🧪 Testing AST Database Service...");
    
    try {
        // Import the compiled JavaScript modules
        const { AstDatabaseService } = require('./dist/services/astdb-service/AstDatabaseService');
        const { EmptyRequest } = require('./dist/shared/proto/common');
        
        console.log("✅ Modules imported successfully");
        
        // Initialize the service
        const astService = AstDatabaseService.getInstance();
        console.log("✅ Service initialized successfully");
        
        // Test 1: Get database status
        console.log("\n📊 Testing database status...");
        const statusResponse = await astService.getDatabaseStatus(EmptyRequest.fromPartial({}));
        console.log("Database status:", statusResponse.status?.astate || "unknown");
        
        // Test 2: Get performance metrics
        console.log("\n📊 Performance metrics...");
        const metrics = astService.getPerformanceMetrics();
        console.log(`Total requests: ${metrics.totalRequests}`);
        console.log(`Success rate: ${metrics.totalRequests > 0 ? ((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1) : 0}%`);
        console.log(`Average response time: ${metrics.averageResponseTime.toFixed(1)}ms`);
        
        // Test 3: Health status
        console.log("\n🏥 Health status...");
        const health = astService.getHealthStatus();
        console.log(`Status: ${health.status.toUpperCase()} ${health.isHealthy ? "✅" : "❌"}`);
        if (health.issues.length > 0) {
            console.log("Issues:");
            health.issues.forEach(issue => console.log(`  - ${issue}`));
        }
        
        console.log("\n🎉 Basic tests completed successfully!");
        
        // Test 4: Try to start a scan (this might fail if tree-sitter is not available)
        console.log("\n🔍 Testing workspace scan (may fail if tree-sitter not available)...");
        try {
            const { StartScanRequest, ScanOptions } = require('./dist/shared/proto/astdb');
            
            const scanRequest = StartScanRequest.fromPartial({
                workspacePath: __dirname,
                options: ScanOptions.fromPartial({
                    maxFiles: 10,
                    includeExtensions: [".js", ".ts"],
                    excludePatterns: ["node_modules", ".git", "out"]
                })
            });
            
            await astService.startWorkspaceScan(scanRequest);
            console.log("✅ Workspace scan started successfully");
            
            // Monitor progress briefly
            let attempts = 0;
            while (attempts < 5) {
                const progressResponse = await astService.getScanProgress(EmptyRequest.fromPartial({}));
                const progress = progressResponse.progress;
                
                if (progress) {
                    console.log(`Progress: ${progress.processedFiles}/${progress.totalFiles} files (${progress.status})`);
                    
                    if (progress.status === "completed" || progress.status === "ready") {
                        console.log("✅ Scan completed successfully");
                        break;
                    }
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                attempts++;
            }
            
        } catch (scanError) {
            console.log("⚠️ Scan test failed (this is expected if tree-sitter is not available):", scanError.message);
        }
        
        console.log("\n🎉 All tests completed!");
        
    } catch (error) {
        console.error("❌ Test failed:", error);
        console.error("Stack trace:", error.stack);
        process.exit(1);
    }
}

// Run the test
testAstDatabase().catch(console.error);
